package oauthservice

import (
	"crypto/rand"
	"encoding/base64"
	"fmt"
	"net/http"
	"net/url"
	"time"

	"github.com/gorilla/mux"
	log "github.com/sirupsen/logrus"
)

// redirectToLoginWithError redirects the user to the login page with error information
func redirectToLoginWithError(w http.ResponseWriter, r *http.Request, errorType, errorDescription string) {
	loginURL := "/login"

	// Preserve any existing query parameters from the original request
	queryParams := r.URL.Query()
	queryParams.Set("error", errorType)
	if errorDescription != "" {
		queryParams.Set("error_description", errorDescription)
	}

	// Construct the final URL
	if len(queryParams) > 0 {
		loginURL += "?" + queryParams.Encode()
	}

	log.Infof("Redirecting to login with error: %s - %s", errorType, errorDescription)
	http.Redirect(w, r, loginURL, http.StatusFound)
}

// InitiateOIDCLoginHandler handles the initial OIDC login request
// It validates the OIDC provider and redirects to the authorization URL
func (service *Service) InitiateOIDCLoginHandler(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	providerID := vars["id"]

	// Get the OIDC provider
	mgr := NewManager(r)
	provider, err := mgr.GetOIDCProvider(providerID)
	if err != nil {
		log.Errorf("Failed to get OIDC provider: %v", err)
		redirectToLoginWithError(w, r, "authentication_failed", "Authentication failed. Please try again.")
		return
	}

	if provider == nil {
		redirectToLoginWithError(w, r, "authentication_failed", "Authentication failed. Please try again.")
		return
	}

	if !provider.Active {
		redirectToLoginWithError(w, r, "authentication_failed", "Authentication failed. Please try again.")
		return
	}

	// Discover the OIDC configuration
	discoveryResponse, failed := getDiscoveryResponse(provider, w, r)
	if failed {
		return
	}

	// Construct the redirect URI to the provider's authorization endpoint
	redirectURI := fmt.Sprintf("https://%s/oidc/callback/%s", service.issuer, providerID)

	// Create the authorization URL with necessary parameters
	authURL, err := url.Parse(discoveryResponse.AuthorizationEndpoint)
	if err != nil {
		log.Errorf("Invalid authorization endpoint: %v", err)
		redirectToLoginWithError(w, r, "authentication_failed", "Authentication failed. Please try again.")
		return
	}

	q := authURL.Query()
	q.Set("client_id", provider.ClientID)
	q.Set("response_type", "code")
	q.Set("scope", provider.Scope)
	q.Set("redirect_uri", redirectURI)

	// Generate a cryptographically secure random state
	state, err := generateSecureRandomString(32)
	if err != nil {
		log.Errorf("Failed to generate state: %v", err)
		redirectToLoginWithError(w, r, "authentication_failed", "Authentication failed. Please try again.")
		return
	}
	q.Set("state", state)

	// Store the state in the session for verification during callback
	err = storeOIDCState(r, w, providerID, state)
	if err != nil {
		log.Errorf("Failed to store OIDC state: %v", err)
		redirectToLoginWithError(w, r, "authentication_failed", "Authentication failed. Please try again.")
		return
	}

	// Store all query parameters in the session if present
	if len(r.URL.Query()) > 0 {
		err = storeQueryParams(r, w, r.URL.Query().Encode())
		if err != nil {
			log.Errorf("Failed to store OIDC parameters: %v", err)
		}
	}

	authURL.RawQuery = q.Encode()

	// Log the redirect for debugging
	log.Infof("Redirecting to OIDC provider: %s", authURL.String())

	// Redirect the user to the authorization URL
	http.Redirect(w, r, authURL.String(), http.StatusFound)
}

func getDiscoveryResponse(provider *OIDCProviderConfig, w http.ResponseWriter, r *http.Request) (*OIDCDiscoveryResponse, bool) {
	discoveryResponse, err := DiscoverOIDCConfiguration(provider.Issuer)
	if err != nil {
		log.Errorf("Failed to discover OIDC configuration: %v", err)
		redirectToLoginWithError(w, r, "authentication_failed", "Authentication failed. Please try again.")
		return nil, true
	}

	// Validate that the required endpoints exist
	if discoveryResponse.AuthorizationEndpoint == "" ||
		discoveryResponse.TokenEndpoint == "" ||
		discoveryResponse.UserinfoEndpoint == "" ||
		discoveryResponse.JwksURI == "" {
		redirectToLoginWithError(w, r, "authentication_failed", "Authentication failed. Please try again.")
		return nil, true
	}
	return discoveryResponse, false
}

// generateSecureRandomString generates a cryptographically secure random string
func generateSecureRandomString(length int) (string, error) {
	b := make([]byte, length)
	_, err := rand.Read(b)
	if err != nil {
		return "", err
	}
	return base64.URLEncoding.EncodeToString(b)[:length], nil
}

// storeOIDCState stores the OIDC state in a secure session cookie
func storeOIDCState(r *http.Request, w http.ResponseWriter, providerID, state string) error {
	// Get or create a session
	if SessionStore == nil {
		return fmt.Errorf("session store not initialized")
	}

	session, err := SessionStore.Get(r, "oidc-session")
	if err != nil {
		log.Errorf("Failed to get oidc session: %v", err)
		return err
	}
	// Set session values
	session.Values["oidc_state_"+providerID] = state
	session.Values["oidc_state_time_"+providerID] = time.Now().Unix()

	// Save the session
	return session.Save(r, w)
}

// storeQueryParams stores all query parameters in a secure session cookie
func storeQueryParams(r *http.Request, w http.ResponseWriter, queryParams string) error {
	// Get or create a session
	if SessionStore == nil {
		return fmt.Errorf("session store not initialized")
	}

	session, err := SessionStore.Get(r, "loginsession")
	if err != nil {
		log.Errorf("Failed to get login session: %v", err)
		return err
	}
	// Set session values
	session.Values["redirect_params"] = queryParams

	// Save the session
	return session.Save(r, w)
}

// getStoredOIDCRedirect retrieves the stored query parameters from the session
func getStoredOIDCRedirect(r *http.Request) (string, error) {
	// Get the session
	if SessionStore == nil {
		return "", fmt.Errorf("session store not initialized")
	}

	session, err := SessionStore.Get(r, "loginsession")
	if err != nil {
		return "", fmt.Errorf("failed to get session: %v", err)
	}

	// Get the redirect parameters from the session
	redirectParams, ok := session.Values["redirect_params"]
	if !ok {
		return "", nil // No redirect parameters stored
	}

	// Return the redirect parameters
	params, ok := redirectParams.(string)
	if !ok {
		return "", fmt.Errorf("invalid redirect parameters format")
	}

	return params, nil
}

// getStoredOIDCState retrieves the stored OIDC state and validates it
func getStoredOIDCState(r *http.Request, providerID string) (string, error) {
	// Get the session
	if SessionStore == nil {
		return "", fmt.Errorf("session store not initialized")
	}

	session, err := SessionStore.Get(r, "oidc-session")
	if err != nil {
		return "", fmt.Errorf("failed to get session: %v", err)
	}

	// Get the state from the session
	stateValue, ok := session.Values["oidc_state_"+providerID]
	if !ok {
		return "", fmt.Errorf("no state found in session")
	}

	// Get the state time from the session
	timeValue, ok := session.Values["oidc_state_time_"+providerID]
	if !ok {
		return "", fmt.Errorf("no state time found in session")
	}

	// Check if the state has expired (10 minutes max)
	stateTime, ok := timeValue.(int64)
	if !ok {
		return "", fmt.Errorf("invalid state time format")
	}

	if time.Now().Unix()-stateTime > 600 { // 10 minutes
		return "", fmt.Errorf("state has expired")
	}

	// Return the state
	state, ok := stateValue.(string)
	if !ok {
		return "", fmt.Errorf("invalid state format")
	}

	return state, nil
}

// clearOIDCState removes the state from the session after it's been used
func clearOIDCState(r *http.Request, w http.ResponseWriter, providerID string) {
	if SessionStore == nil {
		log.Warning("Session store not initialized, can't clear OIDC state")
		return
	}

	session, err := SessionStore.Get(r, "oidc-session")
	if err != nil {
		log.Warningf("Failed to get session: %v", err)
		return
	}

	// Delete the state values
	delete(session.Values, "oidc_state_"+providerID)
	delete(session.Values, "oidc_state_time_"+providerID)

	// Save the session
	if err := session.Save(r, w); err != nil {
		log.Warningf("Failed to save session: %v", err)
	}
}
