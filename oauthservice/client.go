package oauthservice

import (
	"crypto/rand"
	"encoding/base64"

	"time"
)

// Oauth2Client is an oauth2 client
type Oauth2Client struct {
	ClientID                   string
	Label                      string //Label is a just a tag to identity the secret for this ClientID
	Secret                     string
	CallbackURL                string
	ClientCredentialsGrantType bool //ClientCredentialsGrantType indicates if this client can be used in an oauth2 client credentials grant flow
	UserName                   string
}

// NewOauth2Client creates a new NewOauth2Client with a random secret
func NewOauth2Client(clientID, label, callbackURL string, clientCredentialsGrantType bool, username string) *Oauth2Client {
	c := &Oauth2Client{
		ClientID:                   clientID,
		Label:                      label,
		CallbackURL:                callbackURL,
		ClientCredentialsGrantType: clientCredentialsGrantType,
		UserName:                   username,
	}

	randombytes := make([]byte, 39) //Multiple of 3 to make sure no padding is added
	rand.Read(randombytes)
	c.Secret = base64.URLEncoding.EncodeToString(randombytes)
	return c
}

// OIDCProviderConfig represents an OpenID Connect provider configuration
type OIDCProviderConfig struct {
	ID           string `bson:"_id,omitempty" json:"id"`
	Name         string `bson:"name" json:"name"`
	Issuer       string `bson:"issuer" json:"issuer"`
	ClientID     string `bson:"clientId" json:"clientId"`
	ClientSecret string `bson:"clientSecret" json:"clientSecret"`
	Scope        string `bson:"scope" json:"scope"`
	ClaimKey     string `bson:"claimKey,omitempty" json:"claimKey,omitempty"`
	ClaimValue   string `bson:"claimValue,omitempty" json:"claimValue,omitempty"`
	Active       bool   `bson:"active" json:"active"`
	CreatedAt    int64  `bson:"createdAt" json:"createdAt"`
	UpdatedAt    int64  `bson:"updatedAt" json:"updatedAt"`
}

func NewOIDCProviderConfig(name, issuer, clientID, clientSecret, tokenEndpoint, userinfoEndpoint, jwksURI string) *OIDCProviderConfig {
	now := time.Now().Unix()
	return &OIDCProviderConfig{
		Name:         name,
		Issuer:       issuer,
		ClientID:     clientID,
		ClientSecret: clientSecret,
		Scope:        "openid profile email",
		Active:       true,
		CreatedAt:    now,
		UpdatedAt:    now,
	}
}
