package oauthservice

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"regexp"
	"strings"

	"git.gig.tech/gig-meneja/iam/db/user"
	validationdb "git.gig.tech/gig-meneja/iam/db/validation"
	"github.com/gorilla/mux"
	log "github.com/sirupsen/logrus"
	"gopkg.in/mgo.v2"
)

// OIDCTokenResponse represents the response from the token endpoint
type OIDCTokenResponse struct {
	AccessToken  string `json:"access_token"`
	TokenType    string `json:"token_type"`
	RefreshToken string `json:"refresh_token"`
	IDToken      string `json:"id_token"`
}

// OIDCCallbackHandler handles the callback from the OIDC provider
// after the user has authenticated
func (service *Service) OIDCCallbackHandler(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	providerID := vars["id"]

	// Get the authorization code and state from the query parameters
	code := r.URL.Query().Get("code")
	receivedState := r.URL.Query().Get("state")

	if code == "" {
		errorParam := r.URL.Query().Get("error")
		errorDescription := r.URL.Query().Get("error_description")

		if errorParam != "" {
			// The OIDC provider returned an error
			log.Errorf("OIDC provider error: %s - %s", errorParam, errorDescription)

			// Map common OIDC errors to simplified user-friendly messages
			var userErrorType, userErrorDescription string
			switch errorParam {
			case "access_denied":
				userErrorType = "access_denied"
				userErrorDescription = "Authentication was cancelled or access was denied by the provider"
			case "invalid_request":
				userErrorType = "provider_error"
				userErrorDescription = "Invalid authentication request. Please try again"
			case "invalid_client":
				userErrorType = "provider_configuration_error"
				userErrorDescription = "Authentication provider configuration error. Please contact support"
			case "invalid_grant":
				userErrorType = "authentication_expired"
				userErrorDescription = "Authentication session expired. Please try again"
			case "unsupported_response_type":
				userErrorType = "provider_configuration_error"
				userErrorDescription = "Authentication provider configuration error. Please contact support"
			case "invalid_scope":
				userErrorType = "provider_configuration_error"
				userErrorDescription = "Authentication provider configuration error. Please contact support"
			case "server_error":
				userErrorType = "provider_unavailable"
				userErrorDescription = "Authentication provider is temporarily unavailable. Please try again later"
			case "temporarily_unavailable":
				userErrorType = "provider_unavailable"
				userErrorDescription = "Authentication provider is temporarily unavailable. Please try again later"
			default:
				userErrorType = "authentication_failed"
				userErrorDescription = "Authentication failed. Please try again"
			}

			redirectToLoginWithError(w, r, userErrorType, userErrorDescription)
			return
		}

		redirectToLoginWithError(w, r, "authentication_failed", "Authentication failed. Please try again.")
		return
	}

	// Get the OIDC provider
	mgr := NewManager(r)
	provider, err := mgr.GetOIDCProvider(providerID)
	if err != nil {
		log.Errorf("Failed to get OIDC provider: %v", err)
		redirectToLoginWithError(w, r, "authentication_failed", "Authentication failed. Please try again.")
		return
	}

	if provider == nil {
		redirectToLoginWithError(w, r, "authentication_failed", "Authentication failed. Please try again.")
		return
	}

	// Verify the state parameter to prevent CSRF attacks
	storedState, err := getStoredOIDCState(r, providerID)
	if err != nil {
		log.Errorf("Failed to get stored OIDC state: %v", err)
		redirectToLoginWithError(w, r, "authentication_failed", "Authentication failed. Please try again.")
		return
	}

	if receivedState != storedState {
		log.Errorf("State mismatch: received %s, expected %s", receivedState, storedState)
		redirectToLoginWithError(w, r, "authentication_failed", "Authentication failed. Please try again.")
		return
	}

	// Clear the state after it's been used
	clearOIDCState(r, w, providerID)

	// Discover the OIDC configuration for the token endpoint
	discoveryResponse, err := DiscoverOIDCConfiguration(provider.Issuer)
	if err != nil {
		log.Errorf("Failed to discover OIDC configuration: %v", err)
		redirectToLoginWithError(w, r, "authentication_failed", "Authentication failed. Please try again.")
		return
	}

	// Exchange the authorization code for tokens
	redirectURI := fmt.Sprintf("https://%s/oidc/callback/%s", service.issuer, providerID)

	// Prepare the token request
	tokenData := url.Values{}
	tokenData.Set("grant_type", "authorization_code")
	tokenData.Set("code", code)
	tokenData.Set("redirect_uri", redirectURI)
	tokenData.Set("client_id", provider.ClientID)
	tokenData.Set("client_secret", provider.ClientSecret)

	// Make the token request
	tokenReq, err := http.NewRequest("POST", discoveryResponse.TokenEndpoint, strings.NewReader(tokenData.Encode()))
	if err != nil {
		log.Errorf("Failed to create token request: %v", err)
		redirectToLoginWithError(w, r, "authentication_failed", "Authentication failed. Please try again.")
		return
	}

	tokenReq.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	client := &http.Client{}
	tokenResp, err := client.Do(tokenReq)
	if err != nil {
		log.Errorf("Failed to make token request: %v", err)
		redirectToLoginWithError(w, r, "authentication_failed", "Authentication failed. Please try again.")
		return
	}
	defer tokenResp.Body.Close()

	if tokenResp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(tokenResp.Body)
		log.Errorf("Token request failed (%d): %s", tokenResp.StatusCode, body)

		// Provide more specific error messages based on HTTP status code
		switch tokenResp.StatusCode {
		case http.StatusBadRequest:
			redirectToLoginWithError(w, r, "invalid_token_request", "Invalid authentication request. Please try logging in again")
		case http.StatusUnauthorized:
			redirectToLoginWithError(w, r, "invalid_credentials", "Authentication credentials are invalid. Please try again")
		case http.StatusForbidden:
			redirectToLoginWithError(w, r, "access_denied", "Access denied by authentication provider")
		case http.StatusTooManyRequests:
			redirectToLoginWithError(w, r, "rate_limited", "Too many authentication attempts. Please wait and try again later")
		case http.StatusInternalServerError, http.StatusBadGateway, http.StatusServiceUnavailable:
			redirectToLoginWithError(w, r, "provider_unavailable", "Authentication provider is temporarily unavailable. Please try again later")
		default:
			redirectToLoginWithError(w, r, "authentication_failed", "Authentication failed. Please try again")
		}
		return
	}

	// Parse the token response
	var tokenResponse OIDCTokenResponse
	if err := json.NewDecoder(tokenResp.Body).Decode(&tokenResponse); err != nil {
		log.Errorf("Failed to parse token response: %v", err)
		redirectToLoginWithError(w, r, "authentication_failed", "Authentication failed. Please try again.")
		return
	}

	// Use the access token to get user information
	userInfoReq, err := http.NewRequest("GET", discoveryResponse.UserinfoEndpoint, nil)
	if err != nil {
		log.Errorf("Failed to create userinfo request: %v", err)
		redirectToLoginWithError(w, r, "authentication_failed", "Authentication failed. Please try again.")
		return
	}

	userInfoReq.Header.Set("Authorization", fmt.Sprintf("%s %s", tokenResponse.TokenType, tokenResponse.AccessToken))

	userInfoResp, err := client.Do(userInfoReq)
	if err != nil {
		log.Errorf("Failed to make userinfo request: %v", err)
		redirectToLoginWithError(w, r, "authentication_failed", "Authentication failed. Please try again.")
		return
	}
	defer userInfoResp.Body.Close()

	if userInfoResp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(userInfoResp.Body)
		log.Errorf("Userinfo request failed (%d): %s", userInfoResp.StatusCode, body)

		// Provide more specific error messages based on HTTP status code
		switch userInfoResp.StatusCode {
		case http.StatusUnauthorized:
			redirectToLoginWithError(w, r, "invalid_token", "Authentication token is invalid or expired. Please try logging in again")
		case http.StatusForbidden:
			redirectToLoginWithError(w, r, "insufficient_permissions", "Insufficient permissions to access user information. Please contact support")
		case http.StatusTooManyRequests:
			redirectToLoginWithError(w, r, "rate_limited", "Too many requests to authentication provider. Please wait and try again later")
		case http.StatusInternalServerError, http.StatusBadGateway, http.StatusServiceUnavailable:
			redirectToLoginWithError(w, r, "provider_unavailable", "Authentication provider is temporarily unavailable. Please try again later")
		default:
			redirectToLoginWithError(w, r, "userinfo_error", "Failed to retrieve user information. Please try again")
		}
		return
	}

	// Parse the user information
	var userInfo map[string]interface{}
	if err := json.NewDecoder(userInfoResp.Body).Decode(&userInfo); err != nil {
		log.Errorf("Failed to parse userinfo response: %v", err)
		redirectToLoginWithError(w, r, "invalid_userinfo_response", "Invalid user information from authentication provider. Please try again")
		return
	}

	// Log the user info for debugging
	log.Infof("User information from OIDC provider: %+v", userInfo)

	// Validate custom claims if configured
	if provider.ClaimKey != "" {
		if err := validateCustomClaim(provider, userInfo); err != nil {
			log.Errorf("Custom claim validation failed: %v", err)
			redirectToLoginWithError(w, r, "access_denied", "You do not have permission to access this system. Please contact your administrator")
			return
		}
	}

	// Get the email and subject from the userinfo
	email, err := extractEmailFromUserInfo(userInfo)
	if err != nil {
		log.Errorf("Failed to extract email from OIDC userinfo: %v", err)
		redirectToLoginWithError(w, r, "missing_email", "No valid email address found in your account. Please ensure your account has a valid email address and try again.")
		return
	}

	sub, _ := userInfo["sub"].(string)

	if sub == "" {
		log.Error("OIDC provider did not return subject ID")
		redirectToLoginWithError(w, r, "authentication_failed", "Authentication failed. Please try again.")
		return
	}

	// First, try to find the user by OIDC provider and subject ID
	var foundUser *user.User

	// Try to find user by OIDC subject
	foundUser, err = findUserByOIDCSub(r, providerID, sub)
	if err != nil && err.Error() != "not found" {
		log.Errorf("Error looking up user by OIDC sub: %v", err)
		redirectToLoginWithError(w, r, "authentication_failed", "Authentication failed. Please try again.")
		return
	}

	// If not found by OIDC subject, try to find by email
	if foundUser == nil && email != "" {
		foundUser, err = findUserByEmail(r, email)
		if err != nil {
			log.Errorf("Error looking up user by email: %v", err)
			redirectToLoginWithError(w, r, "authentication_failed", "Authentication failed. Please try again.")
			return
		}
	}

	// Store username in session for the login process
	if SessionStore == nil {
		log.Warning("Session store not initialized, can't create user session")
		redirectToLoginWithError(w, r, "authentication_failed", "Authentication failed. Please try again.")
		return
	}

	session, err := SessionStore.Get(r, "loginsession")
	if err != nil {
		log.Errorf("Failed to get login session: %v", err)
		redirectToLoginWithError(w, r, "authentication_failed", "Authentication failed. Please try again.")
		return
	}

	// Determine where to redirect the user
	var redirectURL string

	if foundUser == nil {
		// New user - generate email verification key

		// Extract name fields from userInfo
		firstName, _ := userInfo["given_name"].(string)
		lastName, _ := userInfo["family_name"].(string)

		// If we don't have specific name fields, try to split the full name
		if (firstName == "" || lastName == "") && userInfo["name"] != nil {
			fullName := userInfo["name"].(string)
			nameParts := strings.Split(fullName, " ")
			if len(nameParts) > 0 && firstName == "" {
				firstName = nameParts[0]
			}
			if len(nameParts) > 1 && lastName == "" {
				lastName = strings.Join(nameParts[1:], " ")
			}
		}

		// Generate email validation key using existing service
		mailvalidationkey, err := service.sessionService.RequestEmailValidation(r, email, email, fmt.Sprintf("https://%s/oidc/verify-email", r.Host), "en")
		if err != nil {
			log.Error("Failed to send email verification in OIDC registration flow: ", err)
			redirectToLoginWithError(w, r, "authentication_failed", "Authentication failed. Please try again.")
			return
		}

		// Store user info in session for email verification
		session.Values["oidc_provider"] = providerID
		session.Values["oidc_sub"] = sub
		session.Values["oidc_email"] = email
		session.Values["oidc_firstname"] = firstName
		session.Values["oidc_lastname"] = lastName
		session.Values["oidc_flow_type"] = "registration"
		session.Values["oidc_validation_key"] = mailvalidationkey

		// Redirect to the profile completion page
		redirectURL = "/complete-oidc"
	} else {
		// Existing user - check if this OIDC provider is already linked
		isLinked := isOIDCProviderLinkedToUser(foundUser, providerID, sub)

		if !isLinked {
			mailvalidationkey, err := service.sessionService.RequestEmailValidation(r, foundUser.Username, email, fmt.Sprintf("https://%s/oidc/verify-email", r.Host), "en")
			if err != nil {
				log.Error("Failed to send email verification in OIDC linking flow: ", err)
				redirectToLoginWithError(w, r, "authentication_failed", "Authentication failed. Please try again.")
				return
			}

			session.Values["username"] = foundUser.Username
			session.Values["oidc_provider"] = providerID
			session.Values["oidc_sub"] = sub
			session.Values["oidc_email"] = email
			session.Values["oidc_flow_type"] = "linking"
			session.Values["oidc_validation_key"] = mailvalidationkey

			redirectURL = "/oidc-link-account"
		} else {
			session.Values["username"] = foundUser.Username
			session.Values["oidc_provider"] = providerID
			session.Values["oidc_sub"] = sub

			redirectURL = "/login/oidc"
		}
	}

	// Retrieve stored query parameters and append them to the redirect URL
	storedParams, err := getStoredOIDCRedirect(r)
	if err != nil {
		log.Errorf("Failed to get stored OIDC redirect parameters: %v", err)
	} else if storedParams != "" {
		// Parse the stored parameters to avoid double-encoding
		storedValues, err := url.ParseQuery(storedParams)
		if err != nil {
			log.Errorf("Failed to parse stored OIDC redirect parameters: %v", err)
		} else {
			// Parse the current redirect URL to merge parameters properly
			redirectURLParsed, err := url.Parse(redirectURL)
			if err != nil {
				log.Errorf("Failed to parse redirect URL: %v", err)
			} else {
				// Merge the stored parameters with any existing parameters
				currentParams := redirectURLParsed.Query()
				for key, values := range storedValues {
					for _, value := range values {
						currentParams.Add(key, value)
					}
				}
				redirectURLParsed.RawQuery = currentParams.Encode()
				redirectURL = redirectURLParsed.String()
			}
		}
	}

	log.Infof("About to save session and redirect to: %s", redirectURL)
	if err := session.Save(r, w); err != nil {
		log.Errorf("Failed to save login session: %v", err)
		redirectToLoginWithError(w, r, "authentication_failed", "Authentication failed. Please try again.")
		return
	}
	log.Info("Session saved successfully")

	// Redirect the user to the appropriate page
	log.Infof("Redirecting user to: %s", redirectURL)
	http.Redirect(w, r, redirectURL, http.StatusFound)
	log.Info("Redirect completed")
}

// Function to search for a user by OIDC provider and subject
func findUserByOIDCSub(r *http.Request, providerID, sub string) (*user.User, error) {
	if sub == "" || providerID == "" {
		return nil, fmt.Errorf("provider ID and subject are required")
	}

	userMgr := user.NewManager(r)
	return userMgr.GetByOIDCProviderAndSub(providerID, sub)
}

// Function to search for a user by email address
func findUserByEmail(r *http.Request, email string) (*user.User, error) {
	if email == "" {
		return nil, fmt.Errorf("email is required")
	}

	valMgr := validationdb.NewManager(r)
	validatedEmail, err := valMgr.GetByEmailAddressValidatedEmailAddress(email)
	if err != nil {
		if err == mgo.ErrNotFound {
			return nil, nil // No validated email found
		}
		return nil, err
	}

	userMgr := user.NewManager(r)
	return userMgr.GetByName(validatedEmail.Username)
}

// Function to check if an OIDC provider is already linked to a user
func isOIDCProviderLinkedToUser(user *user.User, providerID, sub string) bool {
	for _, identity := range user.OIDCIdentities {
		if identity.Provider == providerID && identity.Subject == sub {
			return true
		}
	}
	return false
}

// ResendOIDCValidation resends the email validation for OIDC flow
func (service *Service) ResendOIDCValidation(w http.ResponseWriter, r *http.Request) {
	// Get the login session
	if SessionStore == nil {
		log.Warning("Session store not initialized")
		writeJSONErrorResponse(w, "service_unavailable", http.StatusInternalServerError)
		return
	}

	session, err := SessionStore.Get(r, "loginsession")
	if err != nil {
		log.Errorf("Failed to get login session: %v", err)
		writeJSONErrorResponse(w, "session_expired", http.StatusBadRequest)
		return
	}

	// Check if this is an OIDC flow
	flowType, ok := session.Values["oidc_flow_type"].(string)
	if !ok || (flowType != "registration" && flowType != "linking") {
		log.Error("Invalid or missing OIDC flow type in session")
		writeJSONErrorResponse(w, "session_expired", http.StatusBadRequest)
		return
	}

	email, ok := session.Values["oidc_email"].(string)
	if !ok || email == "" {
		log.Error("No email found in OIDC session")
		writeJSONErrorResponse(w, "session_expired", http.StatusBadRequest)
		return
	}

	// Generate new email validation key
	var username string
	if flowType == "registration" {
		username = email // Use email as temporary identifier for new users
	} else {
		username, ok = session.Values["username"].(string)
		if !ok || username == "" {
			log.Error("No username found in linking session")
			writeJSONErrorResponse(w, "session_expired", http.StatusBadRequest)
			return
		}
	}

	// Request new email validation
	mailvalidationkey, err := service.sessionService.RequestEmailValidation(r, username, email, fmt.Sprintf("https://%s/oidc/verify-email", r.Host), "en")
	if err != nil {
		log.Error("Failed to resend email verification: ", err)
		writeJSONErrorResponse(w, "service_unavailable", http.StatusInternalServerError)
		return
	}

	// Update validation key in session
	session.Values["oidc_validation_key"] = mailvalidationkey
	if err := session.Save(r, w); err != nil {
		log.Errorf("Failed to save session: %v", err)
		writeJSONErrorResponse(w, "service_unavailable", http.StatusInternalServerError)
		return
	}

	log.Infof("Email verification resent for OIDC %s flow: %s", flowType, email)
	w.WriteHeader(http.StatusNoContent)
}

// GetOIDCVerificationInfo returns verification info for the OIDC linking flow
func (service *Service) GetOIDCVerificationInfo(w http.ResponseWriter, r *http.Request) {
	if SessionStore == nil {
		log.Warning("Session store not initialized")
		writeJSONErrorResponse(w, "service_unavailable", http.StatusInternalServerError)
		return
	}

	session, err := SessionStore.Get(r, "loginsession")
	if err != nil {
		log.Errorf("Failed to get login session: %v", err)
		writeJSONErrorResponse(w, "session_expired", http.StatusBadRequest)
		return
	}

	flowType, ok := session.Values["oidc_flow_type"].(string)
	if !ok || flowType != "linking" {
		log.Error("Invalid or missing OIDC flow type in session")
		writeJSONErrorResponse(w, "session_expired", http.StatusBadRequest)
		return
	}

	email, ok := session.Values["oidc_email"].(string)
	if !ok || email == "" {
		log.Error("No email found in OIDC session")
		writeJSONErrorResponse(w, "session_expired", http.StatusBadRequest)
		return
	}

	providerID, ok := session.Values["oidc_provider"].(string)
	if !ok || providerID == "" {
		log.Error("No provider found in OIDC session")
		writeJSONErrorResponse(w, "session_expired", http.StatusBadRequest)
		return
	}

	// Get the OIDC provider to retrieve the actual provider name
	mgr := NewManager(r)
	provider, err := mgr.GetOIDCProvider(providerID)
	if err != nil {
		log.Errorf("Failed to get OIDC provider: %v", err)
		writeJSONErrorResponse(w, "provider_configuration_error", http.StatusInternalServerError)
		return
	}

	if provider == nil {
		log.Error("OIDC provider not found")
		writeJSONErrorResponse(w, "provider_configuration_error", http.StatusNotFound)
		return
	}

	response := map[string]interface{}{
		"email":         email,
		"provider_name": provider.Name,
		"flow_type":     flowType,
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// extractEmailFromUserInfo extracts email from OIDC userinfo using multiple possible fields
// Returns the email address if found and valid, otherwise returns an error
func extractEmailFromUserInfo(userInfo map[string]interface{}) (string, error) {
	// Email validation regex pattern
	emailRegex := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)

	// List of possible email fields in order of preference
	// Based on research of common OIDC providers
	emailFields := []string{
		"email",              // Standard OIDC claim
		"upn",                // Microsoft Azure AD User Principal Name
		"unique_name",        // Microsoft Azure AD unique name
		"preferred_username", // Microsoft Azure AD, Keycloak
		"mail",               // Alternative email field (some providers)
		"emailAddress",       // Another variation
		"user_email",         // Custom field used by some providers
	}

	log.Debugf("Extracting email from userinfo with fields: %+v", userInfo)

	for _, field := range emailFields {
		if value, exists := userInfo[field]; exists {
			if emailStr, ok := value.(string); ok && emailStr != "" {
				// Validate that the value is actually an email address
				if emailRegex.MatchString(emailStr) {
					log.Infof("Found valid email in field '%s': %s", field, emailStr)
					return emailStr, nil
				} else {
					log.Debugf("Field '%s' contains value '%s' but it's not a valid email", field, emailStr)
				}
			}
		}
	}

	// If we get here, no valid email was found
	availableFields := make([]string, 0, len(userInfo))
	for key := range userInfo {
		availableFields = append(availableFields, key)
	}

	return "", fmt.Errorf("no valid email address found in userinfo. Available fields: %v", availableFields)
}

// writeJSONErrorResponse writes a consistent JSON error response
func writeJSONErrorResponse(w http.ResponseWriter, errorCode string, statusCode int) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(statusCode)
	response := struct {
		Error string `json:"error"`
	}{
		Error: errorCode,
	}
	json.NewEncoder(w).Encode(&response)
}

// validateCustomClaim validates that the userinfo contains the required claim with the expected value
func validateCustomClaim(provider *OIDCProviderConfig, userInfo map[string]interface{}) error {
	if provider.ClaimKey == "" {
		return nil // No claim validation required
	}

	claimValue, exists := userInfo[provider.ClaimKey]
	if !exists {
		return fmt.Errorf("required claim '%s' not found in user information", provider.ClaimKey)
	}

	// Convert claim value to string for comparison
	claimValueStr, ok := claimValue.(string)
	if !ok {
		return fmt.Errorf("claim '%s' is not a string value", provider.ClaimKey)
	}

	if claimValueStr != provider.ClaimValue {
		return fmt.Errorf("claim '%s' has value '%s' but expected '%s'", provider.ClaimKey, claimValueStr, provider.ClaimValue)
	}

	log.Infof("Custom claim validation successful: %s=%s", provider.ClaimKey, claimValueStr)
	return nil
}
