package oauthservice

import (
	"net/http"
	"time"

	"gopkg.in/mgo.v2"
	"gopkg.in/mgo.v2/bson"

	"fmt"
	"strings"

	"git.gig.tech/gig-meneja/iam/db"
)

const (
	requestsCollectionName            = "oauth_authorizationrequests"
	tokensCollectionName              = "oauth_accesstokens"
	clientsCollectionName             = "oauth_clients"
	refreshTokenCollectionName        = "oauth_refreshtokens"
	oidcProvidersCollectionName       = "oidc_providers"
	passwordLoginStatusCollectionName = "password_login_status"
	oidcProviderLogsCollectionName    = "oidc_provider_logs"
)

// InitModels initialize models in mongo, if required.
func InitModels() {
	index := mgo.Index{
		Key:    []string{"authorizationcode"},
		Unique: true,
	} //Do not drop duplicates since it would hijack another authorizationrequest, better to error out

	db.EnsureIndex(requestsCollectionName, index)

	//TODO: unique username/clientid combination

	automaticExpiration := mgo.Index{
		Key:         []string{"createdat"},
		ExpireAfter: time.Second * 10,
		Background:  true,
	}
	db.EnsureIndex(requestsCollectionName, automaticExpiration)

	index = mgo.Index{
		Key:    []string{"accesstoken"},
		Unique: true,
	} //Do not drop duplicates since it would hijack another authorizationrequest, better to error out

	db.EnsureIndex(tokensCollectionName, index)

	//TODO: unique username/clientid combination

	automaticExpiration = mgo.Index{
		Key:         []string{"createdat"},
		ExpireAfter: AccessTokenExpiration,
		Background:  true,
	}
	db.EnsureIndex(tokensCollectionName, automaticExpiration)

	index = mgo.Index{
		Key:    []string{"clientid", "label"},
		Unique: true,
	}
	db.EnsureIndex(clientsCollectionName, index)

	index = mgo.Index{
		Key:    []string{"refreshtoken"},
		Unique: true,
	} //Do not drop duplicates since it would hijack another refreshtoken, better to error out

	db.EnsureIndex(refreshTokenCollectionName, index)
	automaticExpiration = mgo.Index{
		Key:         []string{"lastused"},
		ExpireAfter: time.Second * 86400 * 30,
		Background:  true,
	}
	db.EnsureIndex(refreshTokenCollectionName, automaticExpiration)

	// Index for OIDC providers
	index = mgo.Index{
		Key:    []string{"name"},
		Unique: true,
	}
	db.EnsureIndex(oidcProvidersCollectionName, index)
}

// Manager is used to store
type Manager struct {
	session *mgo.Session
}

// NewManager creates and initializes a new Manager
func NewManager(r *http.Request) *Manager {
	session := db.GetDBSession(r)
	return &Manager{
		session: session,
	}
}

// NewSessionManager creates manager for given session object
func NewSessionManager(session *mgo.Session) *Manager {
	return &Manager{
		session: session,
	}
}

// ClientManager defines a client persistence interface
type ClientManager interface {
	//AllByClientID retrieves all clients with a given ID
	AllByClientID(clientID string) ([]*Oauth2Client, error)
}

// getAuthorizationRequestCollection returns the mongo collection for the authorizationRequests
func (m *Manager) getAuthorizationRequestCollection() *mgo.Collection {
	return db.GetCollection(m.session, requestsCollectionName)
}

// getAccessTokenCollection returns the mongo collection for the accessTokens
func (m *Manager) getAccessTokenCollection() *mgo.Collection {
	return db.GetCollection(m.session, tokensCollectionName)
}

// getRefreshTokenCollection returns the mongo collection for the accessTokens
func (m *Manager) getRefreshTokenCollection() *mgo.Collection {
	return db.GetCollection(m.session, refreshTokenCollectionName)
}

// getPasswordLoginStatusCollection returns the mongo collection for password login status
func (m *Manager) getPasswordLoginStatusCollection() *mgo.Collection {
	return db.GetCollection(m.session, passwordLoginStatusCollectionName)
}

// getOIDCProviderLogsCollection returns the mongo collection for OIDC provider logs
func (m *Manager) getOIDCProviderLogsCollection() *mgo.Collection {
	return db.GetCollection(m.session, oidcProviderLogsCollectionName)
}

// Get an authorizationRequest by it's authorizationcode.
func (m *Manager) getAuthorizationRequest(authorizationcode string) (ar *authorizationRequest, err error) {
	ar = &authorizationRequest{}

	err = m.getAuthorizationRequestCollection().Find(bson.M{"authorizationcode": authorizationcode}).One(ar)
	if err != nil && err == mgo.ErrNotFound {
		ar = nil
		err = nil
		return
	}
	return
}

// saveAuthorizationRequest stores an authorizationRequest, only adding new authorizationRequests is allowed, updating is not
func (m *Manager) saveAuthorizationRequest(ar *authorizationRequest) (err error) {
	// TODO: Validation!

	err = m.getAuthorizationRequestCollection().Insert(ar)

	return
}

// saveAccessToken stores an accessToken, only adding new accessTokens is allowed, updating is not
func (m *Manager) saveAccessToken(at *AccessToken) (err error) {
	// TODO: Validation!

	err = m.getAccessTokenCollection().Insert(at)

	return
}

// GetAccessToken gets an access token by it's actual token string
// If the token is not found or is expired, nil is returned
func (m *Manager) GetAccessToken(token string) (at *AccessToken, err error) {
	at = &AccessToken{}

	err = m.getAccessTokenCollection().Find(bson.M{"accesstoken": token}).One(at)
	if err != nil && err == mgo.ErrNotFound {
		at = nil
		err = nil
		return
	}
	if err != nil {
		at = nil
		return
	}
	if at.IsExpired() {
		at = nil
		err = nil
	}

	return
}

// RemoveOrganizationScopes removes all user:memberof:globalid scopes from all access tokens
func (m *Manager) RemoveOrganizationScopes(globalID string, username string) error {
	var accessTokens []AccessToken
	qry := bson.M{"username": username}
	err := m.getAccessTokenCollection().Find(qry).All(&accessTokens)
	if err == mgo.ErrNotFound {
		return nil
	}
	if err != nil {
		return err
	}
	for _, accessToken := range accessTokens {
		if !accessToken.IsExpired() {
			memberOfScope := fmt.Sprintf("user:memberof:%s", globalID)
			if strings.Contains(accessToken.Scope, memberOfScope) {
				accessToken.Scope = removeScope(accessToken.Scope, memberOfScope)
				err = m.getAccessTokenCollection().UpdateId(accessToken.ID, accessToken)
				if err != nil && err != mgo.ErrNotFound {
					return err
				}
			}
		}
	}
	return nil
}

func removeScope(scope string, scopeToRemove string) string {
	scopes := []string{}
	split := strings.Split(scope, ",")
	for _, part := range split {
		if part != scopeToRemove {
			scopes = append(scopes, part)
		}
	}
	return strings.Join(scopes, ",")
}

// getRefreshToken gets an refresh token by it's refresh token string
// If the token is not found or is expired, nil is returned
func (m *Manager) getRefreshToken(token string) (rt *refreshToken, err error) {
	rt = &refreshToken{}

	err = m.getRefreshTokenCollection().Find(bson.M{"refreshtoken": token}).One(rt)
	if err == mgo.ErrNotFound {
		rt = nil
		err = nil
	}
	if err != nil {
		rt = nil
	}

	return
}

// saveRefreshToken stores a refreshToken
func (m *Manager) saveRefreshToken(t *refreshToken) (err error) {
	if t == nil {
		return
	}

	_, err = m.getRefreshTokenCollection().Upsert(bson.M{"refreshtoken": t.RefreshToken}, t)

	return
}

// getClientsCollection returns the mongo collection for the clients
func (m *Manager) getClientsCollection() *mgo.Collection {
	return db.GetCollection(m.session, clientsCollectionName)
}

// GetClientLabels returns a list of labels for which there are apikeys registered for a specific client
func (m *Manager) GetClientLabels(clientID string) (labels []string, err error) {
	results := []struct{ Label string }{}
	err = m.getClientsCollection().Find(bson.M{"clientid": clientID}).Select(bson.M{"label": 1}).All(&results)
	labels = make([]string, len(results), len(results))
	for i, value := range results {
		labels[i] = value.Label
	}
	return
}

// CreateClient saves an Oauth2 client
func (m *Manager) CreateClient(client *Oauth2Client) (err error) {

	err = m.getClientsCollection().Insert(client)

	if err != nil && mgo.IsDup(err) {
		err = db.ErrDuplicate
	}
	return
}

// UpdateClient updates the label, callbackurl and clientCredentialsGrantType properties of a client
func (m *Manager) UpdateClient(clientID, oldLabel, newLabel string, callbackURL string, clientcredentialsGrantType bool) (err error) {

	_, err = m.getClientsCollection().UpdateAll(bson.M{"clientid": clientID, "label": oldLabel}, bson.M{"$set": bson.M{"label": newLabel, "callbackurl": callbackURL, "clientcredentialsgranttype": clientcredentialsGrantType}})

	if err != nil && mgo.IsDup(err) {
		err = db.ErrDuplicate
	}
	return
}

// DeleteClient removes a client secret by it's clientID and label
func (m *Manager) DeleteClient(clientID, label string) (err error) {
	_, err = m.getClientsCollection().RemoveAll(bson.M{"clientid": clientID, "label": label})
	return
}

// DeleteAllForOrganization removes al client secrets for the organization
func (m *Manager) DeleteAllForOrganization(clientID string) (err error) {
	_, err = m.getClientsCollection().RemoveAll(bson.M{"clientid": clientID})
	return
}

// GetClient retrieves a client given a clientid and a label
func (m *Manager) GetClient(clientID, label string) (client *Oauth2Client, err error) {
	client = &Oauth2Client{}
	err = m.getClientsCollection().Find(bson.M{"clientid": clientID, "label": label}).One(client)
	if err == mgo.ErrNotFound {
		err = nil
		client = nil
		return
	}
	return
}

// AllByClientID retrieves all clients with a given ID
func (m *Manager) AllByClientID(clientID string) (clients []*Oauth2Client, err error) {
	clients = make([]*Oauth2Client, 0)

	err = m.getClientsCollection().Find(bson.M{"clientid": clientID}).All(&clients)
	return
}

// GetClientByCredentials retrieves a client given a clientid and a secret
func (m *Manager) getClientByCredentials(clientID, secret string) (client *Oauth2Client, err error) {
	client = &Oauth2Client{}
	err = m.getClientsCollection().Find(bson.M{"clientid": clientID, "secret": secret}).One(client)
	if err == mgo.ErrNotFound {
		err = nil
		client = nil
		return
	}
	return
}

// RemoveTokensByGlobalID removes oauth tokens by global id
func (m *Manager) RemoveTokensByGlobalID(globalid string) error {
	_, err := m.getAccessTokenCollection().RemoveAll(bson.M{"globalid": globalid})
	return err
}

// RemoveClientsByID removes oauth clients by client id
func (m *Manager) RemoveClientsByID(clientid string) error {
	_, err := m.getAccessTokenCollection().RemoveAll(bson.M{"clientid": clientid})
	return err
}

// getOIDCProvidersCollection returns the mongo collection for OIDC providers
func (m *Manager) getOIDCProvidersCollection() *mgo.Collection {
	return db.GetCollection(m.session, oidcProvidersCollectionName)
}

// CreateOIDCProvider saves a new OIDC provider configuration
func (m *Manager) CreateOIDCProvider(config *OIDCProviderConfig) error {
	if config.ID == "" {
		config.ID = bson.NewObjectId().Hex()
	}
	err := m.getOIDCProvidersCollection().Insert(config)
	if err != nil && mgo.IsDup(err) {
		return db.ErrDuplicate
	}
	return err
}

// GetOIDCProvider retrieves an OIDC provider by ID
func (m *Manager) GetOIDCProvider(id string) (*OIDCProviderConfig, error) {
	config := &OIDCProviderConfig{}
	err := m.getOIDCProvidersCollection().FindId(id).One(config)
	if err == mgo.ErrNotFound {
		return nil, nil
	}
	return config, err
}

// UpdateOIDCProvider updates an existing OIDC provider configuration
func (m *Manager) UpdateOIDCProvider(config *OIDCProviderConfig) error {
	config.UpdatedAt = time.Now().Unix()
	return m.getOIDCProvidersCollection().UpdateId(config.ID, config)
}

// DeleteOIDCProvider removes an OIDC provider configuration
func (m *Manager) DeleteOIDCProvider(id string) error {
	return m.getOIDCProvidersCollection().RemoveId(id)
}

// ListOIDCProviders returns OIDC provider configurations
// If onlyActive is true, returns only active providers, otherwise returns all providers
func (m *Manager) ListOIDCProviders(onlyActive bool) ([]*OIDCProviderConfig, error) {
	var configs []*OIDCProviderConfig
	query := bson.M{}
	if onlyActive {
		query["active"] = true
	}
	err := m.getOIDCProvidersCollection().Find(query).All(&configs)
	return configs, err
}

// ActivateOIDCProvider sets the provider as active
func (m *Manager) ActivateOIDCProvider(id string) error {
	return m.getOIDCProvidersCollection().UpdateId(id, bson.M{
		"$set": bson.M{
			"active":    true,
			"updatedAt": time.Now().Unix(),
		},
	})
}

// DeactivateOIDCProvider sets the provider as inactive
func (m *Manager) DeactivateOIDCProvider(id string) error {
	return m.getOIDCProvidersCollection().UpdateId(id, bson.M{
		"$set": bson.M{
			"active":    false,
			"updatedAt": time.Now().Unix(),
		},
	})
}

// PasswordLoginStatus represents the enablement status of password login
type PasswordLoginStatus struct {
	Enabled   bool  `json:"enabled" bson:"enabled"`
	UpdatedAt int64 `json:"updatedAt" bson:"updatedAt"`
}

// GetPasswordLoginStatus returns the current password login status
func (m *Manager) GetPasswordLoginStatus() (*PasswordLoginStatus, error) {
	var status PasswordLoginStatus
	err := m.getPasswordLoginStatusCollection().Find(nil).One(&status)
	if err == mgo.ErrNotFound {
		// If no status document exists, create one with default enabled setting
		status = PasswordLoginStatus{
			Enabled:   true,
			UpdatedAt: time.Now().Unix(),
		}
	} else if err != nil {
		return nil, err
	}
	return &status, nil
}

// EnablePasswordLogin sets password login to enabled
func (m *Manager) EnablePasswordLogin() error {
	// Get the first and only document in the collection
	var status PasswordLoginStatus
	err := m.getPasswordLoginStatusCollection().Find(nil).One(&status)
	if err == mgo.ErrNotFound {
		// If no document exists, create one with enabled setting
		status = PasswordLoginStatus{
			Enabled:   true,
			UpdatedAt: time.Now().Unix(),
		}
		return m.getPasswordLoginStatusCollection().Insert(&status)
	} else if err != nil {
		return err
	}

	// Update the first document directly rather than looking up by ID
	status.Enabled = true
	status.UpdatedAt = time.Now().Unix()

	return m.getPasswordLoginStatusCollection().Update(bson.M{}, bson.M{"$set": bson.M{
		"enabled":   true,
		"updatedat": time.Now().Unix(),
	}})
}

// DisablePasswordLogin sets password login to disabled
func (m *Manager) DisablePasswordLogin() error {
	// Get the first and only document in the collection
	var status PasswordLoginStatus
	err := m.getPasswordLoginStatusCollection().Find(nil).One(&status)
	if err == mgo.ErrNotFound {
		// If no document exists, create one with disabled setting
		status = PasswordLoginStatus{
			Enabled:   false,
			UpdatedAt: time.Now().Unix(),
		}
		return m.getPasswordLoginStatusCollection().Insert(&status)
	} else if err != nil {
		return err
	}

	// Update the first document directly rather than looking up by ID
	return m.getPasswordLoginStatusCollection().Update(bson.M{}, bson.M{"$set": bson.M{
		"enabled":   false,
		"updatedat": time.Now().Unix(),
	}})
}

// OIDCProviderLogs represents the logs for OIDC provider operations
type OIDCProviderLogs struct {
	OIDCVersion                  int    `json:"oidcVersion" bson:"oidcVersion"`
	OIDCAction                   string `json:"oidcAction" bson:"oidcAction"`
	OIDCActionSucceeded          bool   `json:"oidcActionSucceeded" bson:"oidcActionSucceeded"`
	OIDCError                    string `json:"oidcError" bson:"oidcError"`
	OIDCProviderID              string `json:"oidcProviderId" bson:"oidcProviderId"`
	PasswordLoginAction          string `json:"passwordLoginAction" bson:"passwordLoginAction"`
	PasswordLoginActionSucceeded bool   `json:"passwordLoginActionSucceeded" bson:"passwordLoginActionSucceeded"`
	PasswordLoginError           string `json:"passwordLoginError" bson:"passwordLoginError"`
	UpdatedAt                    int64  `json:"updatedAt" bson:"updatedAt"`
}

// GetOIDCProviderLogs returns the current OIDC provider logs (first document)
func (m *Manager) GetOIDCProviderLogs() (*OIDCProviderLogs, error) {
	var logs OIDCProviderLogs
	err := m.getOIDCProviderLogsCollection().Find(nil).One(&logs)
	if err == mgo.ErrNotFound {
		// If no logs document exists, create one with default values
		logs = OIDCProviderLogs{
			OIDCVersion:                  0,
			OIDCAction:                   "",
			OIDCActionSucceeded:          true,
			OIDCError:                    "",
			PasswordLoginAction:          "",
			PasswordLoginActionSucceeded: true,
			PasswordLoginError:           "",
			UpdatedAt:                    time.Now().Unix(),
		}
		return &logs, nil
	} else if err != nil {
		return nil, err
	}
	return &logs, nil
}

// SaveOIDCProviderLogs saves or updates the OIDC provider logs (first document)
func (m *Manager) SaveOIDCProviderLogs(logs *OIDCProviderLogs) error {
	logs.UpdatedAt = time.Now().Unix()

	// Try to update the first document, if it doesn't exist, insert it
	_, err := m.getOIDCProviderLogsCollection().Upsert(bson.M{}, logs)
	return err
}
