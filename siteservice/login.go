package siteservice

import (
	"crypto/rand"
	"encoding/json"
	"fmt"
	"math/big"
	"net/http"
	"strings"
	"time"

	"git.gig.tech/gig-meneja/iam/db"
	"git.gig.tech/gig-meneja/iam/oauthservice"
	"github.com/gorilla/sessions"
	"gopkg.in/mgo.v2"

	"net/url"

	"git.gig.tech/gig-meneja/iam/credentials/oauth2"
	"git.gig.tech/gig-meneja/iam/credentials/password"
	"git.gig.tech/gig-meneja/iam/credentials/totp"
	organizationdb "git.gig.tech/gig-meneja/iam/db/organization"
	"git.gig.tech/gig-meneja/iam/db/user"
	validationdb "git.gig.tech/gig-meneja/iam/db/validation"
	"git.gig.tech/gig-meneja/iam/identityservice/invitations"
	"git.gig.tech/gig-meneja/iam/identityservice/organization"
	"git.gig.tech/gig-meneja/iam/tools"
	"git.gig.tech/gig-meneja/iam/validation"
	"github.com/gorilla/mux"
	log "github.com/sirupsen/logrus"
	"gopkg.in/mgo.v2/bson"
)

const (
	mongoLoginCollectionName = "loginsessions"
)

// initLoginModels initialize models in mongo
func (service *Service) initLoginModels() {
	index := mgo.Index{
		Key:      []string{"sessionkey"},
		Unique:   true,
		DropDups: false,
	}

	db.EnsureIndex(mongoLoginCollectionName, index)

	automaticExpiration := mgo.Index{
		Key:         []string{"createdat"},
		ExpireAfter: time.Second * 60 * 10,
		Background:  true,
	}
	db.EnsureIndex(mongoLoginCollectionName, automaticExpiration)

}

type loginSMSSessionInformation struct {
	SessionKey string
	SMSCode    string
	Confirmed  bool
	CreatedAt  time.Time
	PhoneLabel string
}

func newLoginSMSSessionInformation(PhoneLabel string) (sessionInformation *loginSMSSessionInformation, err error) {
	sessionInformation = &loginSMSSessionInformation{CreatedAt: time.Now(), PhoneLabel: PhoneLabel}
	sessionInformation.SessionKey, err = tools.GenerateRandomString()
	if err != nil {
		return
	}
	numbercode, err := rand.Int(rand.Reader, big.NewInt(999999))
	if err != nil {
		return
	}
	sessionInformation.SMSCode = fmt.Sprintf("%06d", numbercode)
	return
}

type loginEmailSessionInformation struct {
	SessionKey string
	EmailCode  string
	Confirmed  bool
	CreatedAt  time.Time
	EmailLabel string
}

func newLoginEmailSessionInformation(EmailLabel string) (sessionInformation *loginEmailSessionInformation, err error) {
	sessionInformation = &loginEmailSessionInformation{CreatedAt: time.Now(), EmailLabel: EmailLabel}
	sessionInformation.SessionKey, err = tools.GenerateRandomString()
	if err != nil {
		return
	}
	numbercode, err := rand.Int(rand.Reader, big.NewInt(999999))
	if err != nil {
		return
	}
	sessionInformation.EmailCode = fmt.Sprintf("%06d", numbercode)
	return
}

// ProcessLoginForm logs a user in if the credentials are valid
func (service *Service) ProcessLoginForm(w http.ResponseWriter, request *http.Request) {
	//TODO: validate csrf token
	//TODO: limit the number of failed/concurrent requests

	err := request.ParseForm()
	if err != nil {
		log.Debug("ERROR parsing registration form")
		http.Error(w, http.StatusText(http.StatusBadRequest), http.StatusBadRequest)
		return
	}
	values := struct {
		Login    string `json:"login"`
		Password string `json:"password"`
	}{}

	if err = json.NewDecoder(request.Body).Decode(&values); err != nil {
		log.Debug("Error decoding the login request:", err)
		http.Error(w, http.StatusText(http.StatusBadRequest), http.StatusBadRequest)
		return
	}

	queryValues := request.URL.Query()
	client := queryValues.Get("client_id")

	login := strings.ToLower(values.Login)

	u, err := organization.SearchUser(request, login)
	if err != nil {
		handleLoginError(w, request, u, err, "Failed to search for user", client)
		return
	}

	// Validate the password
	passwdMgr := password.NewManager(request)
	validPassword, err := passwdMgr.Validate(u.Username, values.Password)
	if err != nil {
		log.Error(err)
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}

	if !validPassword {
		handleLoginError(w, request, u, err, "Invalid password.", client)
		return
	}

	loginSession, err := service.GetSession(request, SessionLogin, "loginsession")
	if err != nil {
		log.Error(err)
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}
	loginSession.Values["username"] = u.Username
	//check if 2fa validity has passed
	if client != "" {

		// Check if we have a valid authorization
		requestedScopes := oauth2.SplitScopeString(request.Form.Get("scope"))
		possibleScopes, err := service.identityService.FilterPossibleScopes(request, u.Username, requestedScopes, true)
		if err != nil {
			log.Error(err)
			http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
			return
		}

		validAuthorization, err := service.verifyExistingAuthorization(request, u.Username, client, possibleScopes)
		if err != nil {
			log.Error("Failed to check if authorization is valid: ", err)
			http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
			return
		}

		// Only attempt to bypass 2fa if we have a valid authorization
		if validAuthorization {
			l2faMgr := organizationdb.NewLast2FAManager(request)
			if l2faMgr.Exists(client, u.Username) {
				timestamp, err := l2faMgr.GetLast2FA(client, u.Username)
				if err != nil {
					log.Error(err)
					http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
					return
				}
				mgr := organizationdb.NewManager(request)
				seconds, err := mgr.GetValidity(client)
				if err != nil {
					log.Error(err)
					http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
					return
				}
				timeconverted := time.Time(timestamp)
				if timeconverted.Add(time.Second * time.Duration(seconds)).After(time.Now()) {
					log.Debug("Try to build protected session")
					service.loginOauthUser(w, request, u.Username)
					return
				}
			}
		}
	}

	sessions.Save(request, w)
	w.WriteHeader(http.StatusNoContent)
}

func handleLoginError(w http.ResponseWriter, request *http.Request, u *user.User, err error, message string, client string) {
	// Remove last 2FA entry if an invalid password is entered
	if client != "" && u != nil {
		l2faMgr := organizationdb.NewLast2FAManager(request)
		if l2faMgr.Exists(client, u.Username) {
			l2faMgr.RemoveLast2FA(client, u.Username)
		}
	}
	log.Error(message, ": ", err)
	http.Error(w, "Invalid username or password.", http.StatusUnauthorized)
}

func (service *Service) verifyExistingAuthorization(request *http.Request, username string, clientID string, possibleScopes []string) (bool, error) {
	authorizedScopes, err := service.identityService.FilterAuthorizedScopes(request, username, clientID, possibleScopes)
	if err != nil {
		log.Error(err)
		return false, err
	}

	var validAuthorization bool

	if authorizedScopes != nil {

		// Check if all authorizations are given
		validAuthorization = oauthservice.IsAuthorizationValid(possibleScopes, authorizedScopes)
		// Check if the user still has the given authorizations
		authorization, err := user.NewManager(request).GetAuthorization(username, clientID)
		if err != nil {
			log.Error("Failed to load authorization: ", err)
			return false, err
		}
		if validAuthorization {
			validAuthorization, err = oauthservice.UserHasAuthorizedScopes(request, authorization)
			if err != nil {
				log.Error("Failed to check if authorizated labels are still present: ", err)
				return false, err
			}
		}

		//Check if we are redirected from the authorize page, it might be that not all authorizations were given,
		// authorize the login but only with the authorized scopes
		referrer := request.Header.Get("Referer")
		if referrer != "" && !validAuthorization { //If we already have a valid authorization, no need to check if we come from the authorize page
			if referrerURL, e := url.Parse(referrer); e == nil {
				validAuthorization = referrerURL.Host == request.Host && referrerURL.Path == "/authorize"
			} else {
				log.Debug("Error parsing referrer: ", e)
			}
		}
	}
	return validAuthorization, err
}

// GetTwoFactorAuthenticationMethods returns the possible two factor authentication methods the user can use to login with.
func (service *Service) GetTwoFactorAuthenticationMethods(w http.ResponseWriter, request *http.Request) {
	loginSession, err := service.GetSession(request, SessionLogin, "loginsession")
	if err != nil {
		log.Error(err)
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}
	username, ok := loginSession.Values["username"].(string)
	if username == "" || !ok {
		http.Error(w, http.StatusText(http.StatusUnauthorized), http.StatusUnauthorized)
		return
	}
	userMgr := user.NewManager(request)
	userFromDB, err := userMgr.GetByName(username)
	if err != nil {
		log.Error(err)
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}

	valMgr := validationdb.NewManager(request)
	alternative2FA, err := getEmailAlternative2FAMechanisms(request, username, userMgr)
	if err != nil {
		log.Error(err)
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}

	response := struct {
		Totp            bool              `json:"totp"`
		Sms             map[string]string `json:"sms"`
		Email           map[string]string `json:"email"`
		DisableEmail2FA bool              `json:"disable_email_2fa"`
	}{Totp: alternative2FA.Totp, Sms: alternative2FA.Sms, Email: make(map[string]string), DisableEmail2FA: userFromDB.DisableEmail2FA}

	if !userFromDB.DisableEmail2FA || (!response.Totp && len(response.Sms) == 0) {
		// Return list of Emails if email 2fa is enabled
		verifiedEmails, err := valMgr.GetByUsernameValidatedEmailAddress(username)
		if err != nil {
			log.Error(err)
			http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
			return
		}
		for _, validatedEmail := range verifiedEmails {
			for _, email := range userFromDB.EmailAddresses {
				if email.EmailAddress == string(validatedEmail.EmailAddress) {
					response.Email[email.Label] = string(validatedEmail.EmailAddress)
				}
			}
		}
	}

	json.NewEncoder(w).Encode(response)
}

// getUserLoggingIn returns an user trying to log in, or an empty string if there is none
func (service *Service) getUserLoggingIn(request *http.Request) (username string, err error) {
	loginSession, err := service.GetSession(request, SessionLogin, "loginsession")
	if err != nil {
		log.Error(err)
		return
	}
	savedusername := loginSession.Values["username"]
	if savedusername != nil {
		username, _ = savedusername.(string)
	}
	return
}

// getSessionKey returns an the login session key, or an empty string if there is none
func (service *Service) getSessionKey(request *http.Request) (sessionKey string, err error) {
	loginSession, err := service.GetSession(request, SessionLogin, "loginsession")
	if err != nil {
		log.Error(err)
		return
	}
	savedSessionKey := loginSession.Values["sessionkey"]
	if savedSessionKey != nil {
		sessionKey, _ = savedSessionKey.(string)
	}
	return
}

// GetSmsCode returns an sms code for a specified phone label
func (service *Service) GetSmsCode(w http.ResponseWriter, request *http.Request) {
	phoneLabel := mux.Vars(request)["phoneLabel"]

	values := struct {
		LangKey string `json:"langkey"`
	}{}
	if err := json.NewDecoder(request.Body).Decode(&values); err != nil {
		log.Debug("Error decoding the GetSmsCode langkey request:", err)
		http.Error(w, http.StatusText(http.StatusBadRequest), http.StatusBadRequest)
		return
	}

	loginSession, err := service.GetSession(request, SessionLogin, "loginsession")
	if err != nil {
		log.Error("Error getting login session", err)
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}
	username, ok := loginSession.Values["username"].(string)
	if username == "" || !ok {
		http.Error(w, http.StatusText(http.StatusUnauthorized), http.StatusUnauthorized)
		return
	}
	userMgr := user.NewManager(request)
	userFromDB, err := userMgr.GetByName(username)
	if err != nil {
		log.Error("Error getting user", err)
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}
	phoneNumber, err := userFromDB.GetPhonenumberByLabel(phoneLabel)
	if err != nil {
		log.Debug(userFromDB.Phonenumbers)
		http.Error(w, http.StatusText(http.StatusNotFound), http.StatusNotFound)
		return
	}
	sessionInfo, err := newLoginSMSSessionInformation(phoneLabel)
	loginSession.Values["sessionkey"] = sessionInfo.SessionKey
	authClientId := loginSession.Values["auth_client_id"]
	authenticatingOrganization := ""
	if authClientId != nil {
		authenticatingOrganization = authClientId.(string)
	}
	mgoCollection := db.GetCollection(db.GetDBSession(request), mongoLoginCollectionName)
	mgoCollection.Insert(sessionInfo)

	translationValues := make(tools.TranslationValues)
	if authenticatingOrganization != "" {
		split := strings.Split(authenticatingOrganization, ".")
		translationValues["authorizeorganizationsms"] = struct {
			Organization string
			Code         string
			Host         string
		}{
			Organization: split[len(split)-1],
			Code:         sessionInfo.SMSCode,
			Host:         request.Host,
		}
	} else {
		translationValues["signinsms"] = struct {
			Code string
			Host string
		}{
			Code: sessionInfo.SMSCode,
			Host: request.Host,
		}
	}

	translations, err := tools.ParseTranslations(values.LangKey, translationValues)
	if err != nil {
		log.Error("Failed to parse translations: ", err)
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}

	smsmessage := ""
	if authenticatingOrganization != "" {
		smsmessage = translations["authorizeorganizationsms"]
	} else {
		smsmessage = translations["signinsms"]
	}

	sessions.Save(request, w)
	go service.smsService.Send(phoneNumber.Phonenumber, smsmessage)
	w.WriteHeader(http.StatusNoContent)
}

// ProcessTOTPConfirmation checks the totp 2 factor authentication code
func (service *Service) ProcessTOTPConfirmation(w http.ResponseWriter, request *http.Request) {
	username, err := service.getUserLoggingIn(request)
	if err != nil {
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}
	if username == "" {
		sessions.Save(request, w)
		http.Error(w, http.StatusText(http.StatusUnauthorized), http.StatusUnauthorized)
		return
	}
	values := struct {
		Totpcode string `json:"code"`
	}{}

	if err := json.NewDecoder(request.Body).Decode(&values); err != nil {
		log.Debug("Error decoding the totp confirmation request:", err)
		http.Error(w, http.StatusText(http.StatusBadRequest), http.StatusBadRequest)
		return
	}
	var validtotpcode bool
	totpMgr := totp.NewManager(request)
	if validtotpcode, err = totpMgr.Validate(username, values.Totpcode); err != nil {
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}
	if !validtotpcode { //TODO: limit to 3 failed attempts
		w.WriteHeader(422)
		return
	}

	//add last 2fa date if logging in with oauth2
	service.storeLast2FALogin(request, username)

	service.loginUser(w, request, username)
}

func (service *Service) getLoginSMSSessionInformation(request *http.Request, sessionKey string) (sessionInfo *loginSMSSessionInformation, err error) {

	if sessionKey == "" {
		sessionKey, err = service.getSessionKey(request)
		if err != nil || sessionKey == "" {
			return
		}
	}

	mgoCollection := db.GetCollection(db.GetDBSession(request), mongoLoginCollectionName)
	sessionInfo = &loginSMSSessionInformation{}
	err = mgoCollection.Find(bson.M{"sessionkey": sessionKey}).One(sessionInfo)
	if err == mgo.ErrNotFound {
		sessionInfo = nil
		err = nil
	}
	return
}
func (service *Service) getLoginEmailSessionInformation(request *http.Request, sessionKey string) (sessionInfo *loginEmailSessionInformation, err error) {

	if sessionKey == "" {
		sessionKey, err = service.getSessionKey(request)
		if err != nil || sessionKey == "" {
			return
		}
	}

	mgoCollection := db.GetCollection(db.GetDBSession(request), mongoLoginCollectionName)
	sessionInfo = &loginEmailSessionInformation{}
	err = mgoCollection.Find(bson.M{"sessionkey": sessionKey}).One(sessionInfo)
	if err == mgo.ErrNotFound {
		sessionInfo = nil
		err = nil
	}
	return
}

// Check2FASMSConfirmation is called by the sms code form to check if the sms is already confirmed on the mobile phone
func (service *Service) Check2FASMSConfirmation(w http.ResponseWriter, request *http.Request) {

	sessionInfo, err := service.getLoginSMSSessionInformation(request, "")
	if err != nil {
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}
	response := map[string]bool{}
	if sessionInfo == nil {
		response["confirmed"] = false
	} else {
		response["confirmed"] = sessionInfo.Confirmed
	}
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)

}

// Process2FASMSConfirmation checks the totp 2 factor authentication code
func (service *Service) Process2FASMSConfirmation(w http.ResponseWriter, request *http.Request) {
	username, err := service.getUserLoggingIn(request)
	if err != nil {
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}
	if username == "" {
		sessions.Save(request, w)
		http.Error(w, http.StatusText(http.StatusUnauthorized), http.StatusUnauthorized)
		return
	}
	values := struct {
		Smscode string `json:"code"`
	}{}

	if err := json.NewDecoder(request.Body).Decode(&values); err != nil {
		log.Debug("Error decoding the 2FA with SMS confirmation request:", err)
		http.Error(w, http.StatusText(http.StatusBadRequest), http.StatusBadRequest)
		return
	}

	sessionInfo, err := service.getLoginSMSSessionInformation(request, "")
	if err != nil {
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}
	if sessionInfo != nil && !sessionInfo.Confirmed {
		//Already confirmed on the phone
		validsmscode := (values.Smscode == sessionInfo.SMSCode)

		if !validsmscode {
			// TODO: limit to 3 failed attempts
			w.WriteHeader(422)
			log.Debugf("Expected code %s, got %s", sessionInfo.SMSCode, values.Smscode)
			return
		}
	}
	loginSession, err := service.GetSession(request, SessionLogin, "loginsession")
	if err != nil {
		log.Error("Failed to get loginsession: ", err)
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}
	validationkey, _ := loginSession.Values["phonenumbervalidationkey"].(string)
	err = service.phonenumberValidationService.ConfirmValidation(request, validationkey, values.Smscode)
	if err == validation.ErrInvalidCode {
		log.Debug("Invalid code")
		// TODO: limit to 3 failed attempts
		w.WriteHeader(422)
		log.Debug("invalid code")
		return
	}
	userMgr := user.NewManager(request)
	userMgr.RemoveExpireDate(username)

	//add last 2fa date if logging in with oauth2
	service.storeLast2FALogin(request, username)

	service.loginUser(w, request, username)
}

func (service *Service) storeLast2FALogin(request *http.Request, username string) {
	//add last 2fa date if logging in with oauth2
	queryValues := request.URL.Query()
	client := queryValues.Get("client_id")
	if client != "" {
		l2faMgr := organizationdb.NewLast2FAManager(request)
		err := l2faMgr.SetLast2FA(client, username)
		if err != nil {
			log.Debug("Error while setting the last 2FA login ", err)
		}
	}
}

func (service *Service) loginUser(w http.ResponseWriter, request *http.Request, username string) {
	service.loginUserWithRedirect(w, request, username, false)
}

func (service *Service) loginUserWithRedirect(w http.ResponseWriter, request *http.Request, username string, shouldRedirect bool) {
	if err := service.SetLoggedInUser(w, request, username); err != nil {
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}
	sessions.Save(request, w)
	log.Debugf("Successfull login by '%s'", username)
	service.loginWithRedirect(w, request, username, shouldRedirect)
}

func (service *Service) loginOauthUser(w http.ResponseWriter, request *http.Request, username string) {
	service.loginOauthUserWithRedirect(w, request, username, false)
}

func (service *Service) loginOauthUserWithRedirect(w http.ResponseWriter, request *http.Request, username string, shouldRedirect bool) {
	if err := service.SetLoggedInOauthUser(w, request, username); err != nil {
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}
	sessions.Save(request, w)
	log.Debugf("Successfull oauth login without 2 factor authentication by '%s'", username)
	service.loginWithRedirect(w, request, username, shouldRedirect)
}

func (service *Service) login(w http.ResponseWriter, request *http.Request, username string) {
	service.loginWithRedirect(w, request, username, false)
}

func (service *Service) loginWithRedirect(w http.ResponseWriter, request *http.Request, username string, shouldRedirect bool) {
	redirectURL := "/"
	queryValues := request.URL.Query()
	endpoint := queryValues.Get("endpoint")
	if endpoint != "" {
		queryValues.Del("endpoint")
		redirectURL = endpoint + "?" + queryValues.Encode()
	} else {
		registrationSession, _ := service.GetSession(request, SessionForRegistration, "registrationdetails")
		if !registrationSession.IsNew && registrationSession.Values["redirectparams"] != nil {
			splitted := strings.Split(registrationSession.Values["redirectparams"].(string), "&")
			if len(splitted) > 3 {
				for _, part := range splitted {
					kv := strings.Split(part, "=")
					if len(kv) == 2 {
						key, _ := url.QueryUnescape(kv[0])
						value, _ := url.QueryUnescape(kv[1])
						queryValues.Set(key, value)
					}
				}
				endpoint, _ = url.QueryUnescape(queryValues.Get("endpoint"))
				queryValues.Del("endpoint")
				redirectURL = endpoint + "?" + queryValues.Encode()
			}
		}
	}

	inviteCode := queryValues.Get("invitecode")
	if inviteCode != "" {
		err := verifyInfoAfterLogin(request, username, inviteCode)
		if err != nil {
			log.Error("Error while running verifyInfoAfterLogin: ", err)
			http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
			return
		}
	}

	sessions.Save(request, w)

	if shouldRedirect {
		// Perform server-side redirect for OIDC flow
		if redirectURL == "/" {
			redirect := queryValues.Get("redirect")
			decodedURL, err := url.QueryUnescape(redirect)
			if err != nil {
				log.Errorf("Error decoding redirect URL: %v", err)
				redirect = "/"
			} else {
				if !strings.HasPrefix(decodedURL, "/") {
					redirect = "/" + decodedURL
				} else {
					redirect = decodedURL
				}
			}
			if redirect != "" {
				redirectURL = redirect
			}
		}

		log.Debug("Performing server-side redirect to:", redirectURL)
		http.Redirect(w, request, redirectURL, http.StatusFound)
	} else {
		// Return JSON response for normal OAuth flow
		response := struct {
			RedirectUrl string `json:"redirecturl"`
		}{}
		response.RedirectUrl = redirectURL
		log.Debug("Returning JSON redirect to:", redirectURL)
		json.NewEncoder(w).Encode(response)
	}
}

func verifyInfoAfterLogin(request *http.Request, username string, inviteCode string) error {
	invitationMgr := invitations.NewInvitationManager(request)
	orgMgr := organizationdb.NewManager(request)
	valMgr := validationdb.NewManager(request)
	userMgr := user.NewManager(request)
	invite, err := invitationMgr.GetByCode(inviteCode)
	if err == mgo.ErrNotFound || invite.Status != invitations.RequestPending {
		// silently ignore
		return nil
	}
	if err != nil {
		return err
	}
	org, err := orgMgr.GetByName(invite.Organization)
	if org == nil {
		log.Warn("Cannot accept invitation of deleted organization: ", invite.Organization)
		return nil
	}
	if invite.Role == invitations.RoleMember {
		err = orgMgr.SaveMember(org, username)
		if err != nil {
			return err
		}
	} else if invite.Role == invitations.RoleOwner {
		err = orgMgr.SaveOwner(org, username)
		if err != nil {
			return err
		}
	}
	if invite.Method == invitations.MethodEmail {
		// Set this email address as verified and create a new one if necessary
		emailAddress := user.EmailAddress{Label: invite.EmailAddress, EmailAddress: invite.EmailAddress}
		err = userMgr.SaveEmail(username, emailAddress)
		if err != nil {
			return err
		}
		validatedEmailAddress := valMgr.NewValidatedEmailAddress(username, invite.EmailAddress)
		err = valMgr.SaveValidatedEmailAddress(validatedEmailAddress)
		if err != nil {
			return err
		}
	} else if invite.Method == invitations.MethodPhone {
		// Set this phone number as verified and create a new one if necessary
		phoneNumber := user.Phonenumber{Label: invite.PhoneNumber, Phonenumber: invite.PhoneNumber}
		err = userMgr.SavePhone(username, phoneNumber)
		if err != nil {
			return err
		}
		validatedPhoneNumber := valMgr.NewValidatedPhonenumber(username, invite.PhoneNumber)
		err = valMgr.SaveValidatedPhonenumber(validatedPhoneNumber)
		if err != nil {
			return err
		}
	}
	return invitationMgr.SetAcceptedByCode(inviteCode)
}

// ValidateEmail is the handler for POST /login/validateemail
func (service *Service) ValidateEmail(w http.ResponseWriter, r *http.Request) {
	body := struct {
		Username string `json:"username"`
		LangKey  string `json:"langkey"`
	}{}

	if err := json.NewDecoder(r.Body).Decode(&body); err != nil {
		log.Debug("Error decoding the validted email request:", err)
		http.Error(w, http.StatusText(http.StatusBadRequest), http.StatusBadRequest)
		return
	}

	// try to load the username if it is a validated phone number
	valMgr := validationdb.NewManager(r)
	userFromPhone, err := valMgr.GetByPhoneNumber(body.Username)
	if err != nil && !db.IsNotFound(err) {
		log.Error("Error while retrieving username from phone number: ", err)
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}
	if !db.IsNotFound(err) {
		body.Username = userFromPhone.Username
	}

	userMgr := user.NewManager(r)
	user, err := userMgr.GetByName(body.Username)
	if err != nil {
		if db.IsNotFound(err) {
			// Log the actual error but return a generic success response to prevent username enumeration
			log.Debug("Username not found: ", body.Username)
			// Return success status to prevent username enumeration
			w.WriteHeader(http.StatusOK)
			return
		}
		log.Error("Error while retrieving username: ", err)
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}

	if len(user.EmailAddresses) < 1 {
		log.Debug("User does not have any email addresses.")
		// Return success status to prevent username enumeration
		w.WriteHeader(http.StatusOK)
		return
	}

	// Don't send verification if at least 1 email address is already verified
	ve, err := valMgr.GetByUsernameValidatedEmailAddress(body.Username)
	if err != nil && !db.IsNotFound(err) {
		log.Error("Error while retrieving verified email addresses: ", err)
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}
	//User has verified email addresses
	if err == nil && len(ve) > 0 {
		// Log the actual issue but return a generic success response
		log.Debug("User has verified email addresses, no need to send validation")
		// Return success status to prevent username enumeration
		w.WriteHeader(http.StatusOK)
		return
	}
	// Don't send verification if one is already ongoing
	ov, err := valMgr.GetOngoingEmailAddressValidationByUser(body.Username)
	if err != nil && !db.IsNotFound(err) {
		log.Error("Error while checking ongoing email address verifications: ", err)
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}
	if err == nil && len(ov) > 0 {
		// Log the actual issue but return a generic success response
		log.Debug("User has ongoing email address verifications, no need to send another")
		// Return success status to prevent username enumeration
		w.WriteHeader(http.StatusOK)
		return
	}

	for _, email := range user.EmailAddresses {
		_, err = service.emailaddressValidationService.RequestValidation(r, body.Username, email.EmailAddress, fmt.Sprintf("https://%s/emailvalidation", r.Host), body.LangKey)
		if err != nil {
			log.Error("Failed to validate email address: ", err)
			http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
			return
		}
	}

	w.WriteHeader(http.StatusOK)
}

// ForgotPassword handler for POST /login/forgotpassword
func (service *Service) ForgotPassword(w http.ResponseWriter, request *http.Request) {
	// login can be username or email
	values := struct {
		Login   string `json:"login"`
		LangKey string `json:"langkey"`
	}{}

	if err := json.NewDecoder(request.Body).Decode(&values); err != nil {
		log.Debug("Error decoding the ForgotPassword request:", err)
		http.Error(w, http.StatusText(http.StatusBadRequest), http.StatusBadRequest)
		return
	}
	// Make field case insensitive
	values.Login = strings.ToLower(values.Login)
	userMgr := user.NewManager(request)
	valMgr := validationdb.NewManager(request)
	validatedemail, err := valMgr.GetByEmailAddressValidatedEmailAddress(values.Login)
	// If validated email send request
	if err == nil {
		var emails []string
		emails = []string{validatedemail.EmailAddress}
		sendPasswordResetEmail(validatedemail.Username, emails, service, request, values.LangKey, w)
		return
	}
	if !db.IsNotFound(err) {
		log.Errorf("Failed to send password reset email - %s ", err)
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}

	validatedPhone, err := valMgr.GetByPhoneNumber(values.Login)
	if err == nil {
		values.Login = validatedPhone.Username
		usr, err := userMgr.GetByName(values.Login)

		if err == nil {
			username := usr.Username
			validatedemails, err := valMgr.GetByUsernameValidatedEmailAddress(username)

			if err == nil {
				if validatedemails == nil || len(validatedemails) == 0 {
					log.Errorf("Username %s does not have a validated email address.", username)
					w.WriteHeader(http.StatusNoContent)
					return
				}
				emails := make([]string, len(validatedemails))
				for idx, validatedemail := range validatedemails {
					emails[idx] = validatedemail.EmailAddress
				}

				sendPasswordResetEmail(username, emails, service, request, values.LangKey, w)
				return
			}
		}
		// Log the error but return a generic response
		log.Errorf("Username %s does not have a validated email address.", values.Login)
		w.WriteHeader(http.StatusNoContent)
		return
	}

	if !db.IsNotFound(err) {
		log.Errorf("Failed to send password reset email - %s ", err)
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}

	// no validated email addresses or phone numbers
	log.Infof("No validated email address or phone number was found for '%s'.", values.Login)
	w.WriteHeader(http.StatusNoContent)
}

func sendPasswordResetEmail(username string, emails []string, service *Service, request *http.Request, LangKey string, w http.ResponseWriter) error {
	_, err := service.emailaddressValidationService.RequestPasswordReset(request, username, emails, LangKey)
	if err != nil {
		log.Errorf("Failed to send password reset email - %s ", err)
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
	}
	w.WriteHeader(http.StatusNoContent)
	return err
}

func handleForgotPasswordError(w http.ResponseWriter) {
	http.Error(w, "Failed to request password reset.", http.StatusUnauthorized)
}

// ResetPassword handler for POST /login/resetpassword
func (service *Service) ResetPassword(w http.ResponseWriter, request *http.Request) {
	values := struct {
		Token    string `json:"token"`
		Password string `json:"password"`
	}{}

	if err := json.NewDecoder(request.Body).Decode(&values); err != nil {
		log.Debug("Error decoding the ResetPassword request:", err)
		http.Error(w, http.StatusText(http.StatusBadRequest), http.StatusBadRequest)
		return
	}
	pwdMngr := password.NewManager(request)
	token, err := pwdMngr.FindResetToken(values.Token)
	if err != nil {
		log.Debug("Failed to find password reset token - ", err)
		// Return a generic error to prevent token enumeration
		http.Error(w, "Invalid or expired password reset token", http.StatusBadRequest)
		return
	}
	err = pwdMngr.Save(token.Username, values.Password)
	if err != nil {
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}
	if err = pwdMngr.DeleteResetToken(values.Token); err != nil {
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return

	}
	w.WriteHeader(http.StatusNoContent)
	return
}

// LoginResendPhonenumberConfirmation resend the phone number confirmation after logging in to a possibly new phone number
func (service *Service) LoginResendPhonenumberConfirmation(w http.ResponseWriter, request *http.Request) {
	values := struct {
		LangKey string `json:"langkey"`
	}{}

	if err := json.NewDecoder(request.Body).Decode(&values); err != nil {
		log.Debug("Error decoding the ResendPhonenumberConfirmation request: ", err)
		http.Error(w, http.StatusText(http.StatusBadRequest), http.StatusBadRequest)
		return
	}
	loginSession, err := service.GetSession(request, SessionLogin, "loginsession")
	if err != nil {
		log.Error(err)
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}
	if loginSession.IsNew {
		sessions.Save(request, w)
		log.Debug("Login session expired")
		http.Error(w, http.StatusText(http.StatusUnauthorized), http.StatusUnauthorized)
		return
	}
	oldSessionInfo, err := service.getLoginSMSSessionInformation(request, loginSession.Values["sessionkey"].(string))

	if oldSessionInfo == nil {
		writeErrorResponse(w, "smsconfirmation_session_not_available", http.StatusUnauthorized)
		return
	}

	username, _ := loginSession.Values["username"].(string)

	userMgr := user.NewManager(request)
	userFromDB, err := userMgr.GetByName(username)
	if err != nil {
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}

	phoneNumber, err := userFromDB.GetPhonenumberByLabel(oldSessionInfo.PhoneLabel)
	if err != nil {
		log.Debug(userFromDB.Phonenumbers)
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}
	sessionInfo, err := newLoginSMSSessionInformation(oldSessionInfo.PhoneLabel)
	if err != nil {
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}
	loginSession.Values["sessionkey"] = sessionInfo.SessionKey

	mgoCollection := db.GetCollection(db.GetDBSession(request), mongoLoginCollectionName)
	mgoCollection.Insert(sessionInfo)

	TranslationValues := tools.TranslationValues{
		"signinsms": struct {
			Code string
			Host string
		}{
			Code: sessionInfo.SMSCode,
			Host: request.Host,
		},
	}

	translations, err := tools.ParseTranslations(values.LangKey, TranslationValues)
	if err != nil {
		log.Error("Failed to parse translations: ", err)
		return
	}

	go service.phonenumberValidationService.SMSService.Send(phoneNumber.Phonenumber,
		translations["signinsms"])

	sessions.Save(request, w)
	w.WriteHeader(http.StatusNoContent)
}

func (service *Service) GetOrganizationInvitation(w http.ResponseWriter, request *http.Request) {
	code := mux.Vars(request)["code"]
	if code == "" {
		http.Error(w, http.StatusText(http.StatusBadRequest), http.StatusBadRequest)
		return
	}
	invitationMgr := invitations.NewInvitationManager(request)
	invite, err := invitationMgr.GetByCode(code)
	if err == mgo.ErrNotFound {
		w.WriteHeader(http.StatusNotFound)
	} else {
		json.NewEncoder(w).Encode(invite)
	}
}
func (service *Service) GetEmailCode(w http.ResponseWriter, request *http.Request) {
	emailLabel := mux.Vars(request)["emailLabel"]

	values := struct {
		LangKey string `json:"langkey"`
	}{}
	if err := json.NewDecoder(request.Body).Decode(&values); err != nil {
		log.Error("Error decoding the GetEmailCode langkey request:", err)
		http.Error(w, http.StatusText(http.StatusBadRequest), http.StatusBadRequest)
		return
	}

	loginSession, err := service.GetSession(request, SessionLogin, "loginsession")
	if err != nil {
		log.Error("Error getting login session", err)
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}
	username, ok := loginSession.Values["username"].(string)
	if username == "" || !ok {
		http.Error(w, http.StatusText(http.StatusUnauthorized), http.StatusUnauthorized)
		return
	}
	userMgr := user.NewManager(request)
	userFromDB, err := userMgr.GetByName(username)
	if err != nil {
		log.Error("Error getting user", err)
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}
	if userFromDB.DisableEmail2FA {
		alternative2FA, err := getEmailAlternative2FAMechanisms(request, username, userMgr)
		if err != nil || alternative2FA.Totp || len(alternative2FA.Sms) > 0 {
			log.Error("Email 2FA is disabled for this user")
			http.Error(w, "Email 2FA is disabled for this user", http.StatusForbidden)
			return
		}
	}
	email, err := userFromDB.GetEmailAddressByLabel(emailLabel)
	if err != nil {
		log.Debug(userFromDB.EmailAddresses)
		http.Error(w, http.StatusText(http.StatusNotFound), http.StatusNotFound)
		return
	}
	sessionInfo, err := newLoginEmailSessionInformation(emailLabel)
	loginSession.Values["sessionkey"] = sessionInfo.SessionKey
	authClientId := loginSession.Values["auth_client_id"]
	authenticatingOrganization := ""
	if authClientId != nil {
		authenticatingOrganization = authClientId.(string)
	}
	mgoCollection := db.GetCollection(db.GetDBSession(request), mongoLoginCollectionName)
	mgoCollection.Insert(sessionInfo)

	translationValues := make(tools.TranslationValues)
	if authenticatingOrganization != "" {
		split := strings.Split(authenticatingOrganization, ".")
		translationValues["authorizeorganizationsms"] = struct {
			Organization string
			Code         string
			Host         string
		}{
			Organization: split[len(split)-1],
			Code:         sessionInfo.EmailCode,
			Host:         request.Host,
		}
	} else {
		translationValues["signinsms"] = struct {
			Code string
			Host string
		}{
			Code: sessionInfo.EmailCode,
			Host: request.Host,
		}
	}
	translationValues["email2fa_title"] = struct {
		Host string
	}{Host: request.Host}
	translationValues["email2fa_reason"] = struct {
		Host string
	}{Host: request.Host}
	translationValues["email2fa_subject"] = struct {
		Host string
	}{Host: request.Host}

	translations, err := tools.ParseTranslations(values.LangKey, translationValues)
	if err != nil {
		log.Error("Failed to parse translations: ", err)
		return
	}

	emailMessage := ""
	if authenticatingOrganization != "" {
		emailMessage = translations["authorizeorganizationsms"]
	} else {
		emailMessage = translations["signinsms"]
	}

	templateParameters := validation.EmailWithCodeTemplateParams{
		Username: username,
		Title:    translations["email2fa_title"],
		Text:     emailMessage,
		Reason:   translations["email2fa_reason"],
		Host:     request.Host,
	}

	sessions.Save(request, w)
	service.emailaddressValidationService.Send2FAEmail(request, templateParameters, email.EmailAddress, translations["email2fa_subject"])

	w.WriteHeader(http.StatusNoContent)
}

func (service *Service) Process2FAEmailConfirmation(w http.ResponseWriter, request *http.Request) {
	username, err := service.getUserLoggingIn(request)
	if err != nil {
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}
	if username == "" {
		sessions.Save(request, w)
		http.Error(w, http.StatusText(http.StatusUnauthorized), http.StatusUnauthorized)
		return
	}

	values := struct {
		EmailCode string `json:"code"`
	}{}

	if err := json.NewDecoder(request.Body).Decode(&values); err != nil {
		log.Debug("Error decoding the 2FA with Email confirmation request:", err)
		http.Error(w, http.StatusText(http.StatusBadRequest), http.StatusBadRequest)
		return
	}

	sessionInfo, err := service.getLoginEmailSessionInformation(request, "")
	if err != nil {
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}

	if sessionInfo != nil && !sessionInfo.Confirmed {
		//Already confirmed on the phone
		validEmailCode := (values.EmailCode == sessionInfo.EmailCode)

		if !validEmailCode {
			// TODO: limit to 3 failed attempts
			w.WriteHeader(422)
			log.Debugf("Expected code %s, got %s", sessionInfo.EmailCode, values.EmailCode)
			return
		}
	}

	userMgr := user.NewManager(request)
	userMgr.RemoveExpireDate(username)

	//add last 2fa date if logging in with oauth2
	service.storeLast2FALogin(request, username)

	service.loginUser(w, request, username)
}
func (service *Service) LoginResendEmailConfirmation(w http.ResponseWriter, request *http.Request) {
	values := struct {
		LangKey string `json:"langkey"`
	}{}

	if err := json.NewDecoder(request.Body).Decode(&values); err != nil {
		log.Debug("Error decoding the ResendPhonenumberConfirmation request: ", err)
		http.Error(w, http.StatusText(http.StatusBadRequest), http.StatusBadRequest)
		return
	}
	loginSession, err := service.GetSession(request, SessionLogin, "loginsession")
	if err != nil {
		log.Error(err)
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}
	if loginSession.IsNew {
		sessions.Save(request, w)
		log.Debug("Login session expired")
		http.Error(w, http.StatusText(http.StatusUnauthorized), http.StatusUnauthorized)
		return
	}
	oldSessionInfo, err := service.getLoginEmailSessionInformation(request, loginSession.Values["sessionkey"].(string))

	if oldSessionInfo == nil {
		writeErrorResponse(w, "emailconfirmation_session_not_available", http.StatusUnauthorized)
		return
	}

	username, _ := loginSession.Values["username"].(string)

	userMgr := user.NewManager(request)
	userFromDB, err := userMgr.GetByName(username)
	if err != nil {
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}

	email, err := userFromDB.GetEmailAddressByLabel(oldSessionInfo.EmailLabel)
	if err != nil {
		log.Debug(userFromDB.EmailAddresses)
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}
	sessionInfo, err := newLoginEmailSessionInformation(oldSessionInfo.EmailLabel)
	if err != nil {
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}
	loginSession.Values["sessionkey"] = sessionInfo.SessionKey

	mgoCollection := db.GetCollection(db.GetDBSession(request), mongoLoginCollectionName)
	mgoCollection.Insert(sessionInfo)

	TranslationValues := tools.TranslationValues{
		"signinsms": struct {
			Code string
			Host string
		}{
			Code: sessionInfo.EmailCode,
			Host: request.Host,
		},
		"email2fa_title": struct {
			Host string
		}{Host: request.Host},
		"email2fa_reason": struct {
			Host string
		}{Host: request.Host},
		"email2fa_subject": struct {
			Host string
		}{Host: request.Host},
	}

	translations, err := tools.ParseTranslations(values.LangKey, TranslationValues)
	if err != nil {
		log.Error("Failed to parse translations: ", err)
		return
	}

	templateParameters := validation.EmailWithCodeTemplateParams{
		Username: username,
		Title:    translations["email2fa_title"],
		Text:     translations["signinsms"],
		Reason:   translations["email2fa_reason"],
		Host:     request.Host,
	}

	sessions.Save(request, w)

	err = service.emailaddressValidationService.Send2FAEmail(request, templateParameters, email.EmailAddress, translations["email2fa_subject"])

	if err != nil {
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
	}

	w.WriteHeader(http.StatusNoContent)
}

func getEmailAlternative2FAMechanisms(r *http.Request, username string, userMgr *user.Manager) (*struct {
	Totp bool
	Sms  map[string]string
}, error) {
	userFromDB, err := userMgr.GetByName(username)
	if err != nil {
		return nil, err
	}
	totpMgr := totp.NewManager(r)
	hasTOTP, err := totpMgr.HasTOTP(username)
	if err != nil {
		return nil, err
	}
	valMgr := validationdb.NewManager(r)
	verifiedPhones, err := valMgr.GetByUsernameValidatedPhonenumbers(username)
	if err != nil {
		return nil, err
	}
	sms := make(map[string]string)
	for _, validatedPhoneNumber := range verifiedPhones {
		for _, number := range userFromDB.Phonenumbers {
			if number.Phonenumber == string(validatedPhoneNumber.Phonenumber) {
				sms[number.Label] = string(validatedPhoneNumber.Phonenumber)
			}
		}
	}
	return &struct {
		Totp bool
		Sms  map[string]string
	}{hasTOTP, sms}, nil
}

// redirectToLoginWithError redirects the user to the login page with error information
func redirectToLoginWithError(w http.ResponseWriter, r *http.Request, errorType, errorDescription string) {
	loginURL := "/login"

	// Preserve any existing query parameters from the original request
	queryParams := r.URL.Query()
	queryParams.Set("error", errorType)
	if errorDescription != "" {
		queryParams.Set("error_description", errorDescription)
	}

	// Construct the final URL
	if len(queryParams) > 0 {
		loginURL += "?" + queryParams.Encode()
	}

	log.Infof("Redirecting to login with error: %s - %s", errorType, errorDescription)
	http.Redirect(w, r, loginURL, http.StatusFound)
}

// ProcessOIDCLogin completes the login process for a user authenticated via OIDC
func (service *Service) ProcessOIDCLogin(w http.ResponseWriter, r *http.Request) {
	loginSession, err := service.GetSession(r, SessionLogin, "loginsession")
	if err != nil {
		log.Error(err)
		writeErrorResponse(w, "authentication_failed", http.StatusBadRequest)
		return
	}

	username, ok := loginSession.Values["username"].(string)
	if !ok || username == "" {
		writeErrorResponse(w, "authentication_failed", http.StatusBadRequest)
		return
	}

	flowType, _ := loginSession.Values["oidc_flow_type"].(string)
	if flowType == "linking" {
		// Parse request body for email verification code
		var requestData struct {
			EmailCode string `json:"email_code"`
		}
		if err := json.NewDecoder(r.Body).Decode(&requestData); err != nil {
			log.Error("Error decoding OIDC login request:", err)
			writeErrorResponse(w, "invalid_request", http.StatusBadRequest)
			return
		}

		if requestData.EmailCode == "" {
			writeErrorResponse(w, "email_verification_code_required", http.StatusBadRequest)
			return
		}

		validationKey, ok := loginSession.Values["oidc_validation_key"].(string)
		if !ok || validationKey == "" {
			writeErrorResponse(w, "email_verification_session_not_found", http.StatusBadRequest)
			return
		}

		err = service.emailaddressValidationService.ConfirmValidation(r, validationKey, requestData.EmailCode)
		if err != nil {
			log.Error("Email verification failed:", err)
			writeErrorResponse(w, "invalid_email_verification_code", http.StatusBadRequest)
			return
		}
	}

	providerID, providerOk := loginSession.Values["oidc_provider"].(string)
	sub, subOk := loginSession.Values["oidc_sub"].(string)
	email, _ := loginSession.Values["oidc_email"].(string)

	if providerOk && subOk && providerID != "" && sub != "" {
		err := linkOIDCToExistingUser(r, w, username, providerID, sub, email)
		if err != nil {
			log.Errorf("Error linking OIDC identity to user: %v", err)
			writeErrorResponse(w, "authentication_failed", http.StatusInternalServerError)
			return
		}
	}

	service.loginUserWithRedirect(w, r, username, true)
}

// Function to link an OIDC identity to an existing user
func linkOIDCToExistingUser(r *http.Request, w http.ResponseWriter, username string, providerID, sub, email string) error {

	userMgr := user.NewManager(r)
	existingUser, err := userMgr.GetByName(username)
	if err != nil {
		log.Errorf("Error looking up user: %v", err)
		return err
	}

	// Check if user already has this OIDC identity
	for _, identity := range existingUser.OIDCIdentities {
		if identity.Provider == providerID && identity.Subject == sub && identity.Email == email {
			// Just update the last login time
			identity.LastLogin = time.Now()
			if err := userMgr.Save(existingUser); err != nil {
				log.Errorf("Error saving user: %v", err)
				return err
			}
			return nil
		}
	}

	// Add this OIDC identity to the user
	existingUser.OIDCIdentities = append(existingUser.OIDCIdentities, user.OIDCIdentity{
		Provider:  providerID,
		Subject:   sub,
		Email:     email,
		LastLogin: time.Now(),
	})

	// Save the updated user
	if err := userMgr.Save(existingUser); err != nil {
		log.Errorf("Error saving user: %v", err)
		return err
	}

	return nil
}
