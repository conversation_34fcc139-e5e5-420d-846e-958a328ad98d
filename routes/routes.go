package routes

import (
	"net/http"

	"github.com/gorilla/csrf"
	"github.com/gorilla/handlers"
	"github.com/gorilla/mux"

	"git.gig.tech/gig-meneja/iam/db"
	"git.gig.tech/gig-meneja/iam/identityservice"
	"git.gig.tech/gig-meneja/iam/oauthservice"
	"git.gig.tech/gig-meneja/iam/siteservice"
	log "github.com/sirupsen/logrus"
)

// GetRouter contructs the router hierarchy and registers all handlers and middleware
func GetRouter(sc *siteservice.Service, is *identityservice.Service, oauthsc *oauthservice.Service) http.Handler {
	CSRF := csrf.Protect([]byte("32-byte-long-auth-key"), csrf.SameSite(csrf.SameSiteStrictMode))
	r := mux.NewRouter().StrictSlash(true)

	apiRouter := r.PathPrefix("/api").Subrouter()

	apiRouter.HandleFunc("/system/health-check", HealthCheckHandler).Methods("Get")

	is.AddRoutes(apiRouter)
	oauthsc.AddRoutes(r)

	dbmw := db.DBMiddleware()
	sc.AddRoutes(r, CSRF, dbmw)
	sc.InitModels()

	// Add middlewares
	recovery := handlers.RecoveryHandler()

	// Content security policy header
	securityHeaders := func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			// Set security headers
			w.Header().Set("Strict-Transport-Security", "max-age=31536000; includeSubDomains")
			w.Header().Set("Content-Security-Policy", "default-src 'self'; style-src 'self' 'unsafe-inline';")
			w.Header().Set("X-Frame-Options", "DENY")
			w.Header().Set("X-Content-Type-Options", "nosniff")

			// Hide server version information by setting a generic server header
			// This will override the default Go server header that includes version info
			w.Header().Set("Server", "Server")

			next.ServeHTTP(w, r)
		})
	}
	router := NewRouter(r)

	router.Use(recovery, LoggingMiddleware, dbmw, sc.SetWebUserMiddleWare, securityHeaders)

	return router.Handler()
}

func HealthCheckHandler(writer http.ResponseWriter, request *http.Request) {
	conn := db.NewSession()
	defer conn.Close()
	if conn == nil {
		http.Error(writer, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		log.Error("Error connecting to MongoDB")
		return
	}
	err := conn.Ping()
	if err != nil {
		http.Error(writer, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		log.Error("PING MongoDB returned error", err.Error())
		return
	}

	writer.WriteHeader(http.StatusOK)
}
