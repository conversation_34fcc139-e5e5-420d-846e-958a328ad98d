#%RAML 1.0
title: iam
version: 0.1
baseUri: https://iam.gig.tech/api
securitySchemes:
  oauth20:
    description: |
      OAuth2 is a protocol that lets external apps request authorization to private
      details in a user's account without getting their password.
    type: OAuth20
    describedBy:
      headers:
        Authorization:
          description: |
            Used to send a valid OAuth 2 access token. Do not use together with
            the "access_token" query string parameter.
      queryParameters:
        access_token:
          description: |
            Used to send a valid OAuth 2 access token. Do not use together with
            the "Authorization" header
      responses:
        401:
          description: Unauthorized
        403:
          description: Forbidden
    settings:
      authorizationUri: https://iam.gig.tech/v1/oauth/authorize
      accessTokenUri: https://iam.gig.tech/v1/oauth/access_token
      authorizationGrants: [ authorization_code, client_credentials ]
      scopes:
        - "user:admin"
        - "user:info"
        - "user:keystore"
        - "organization:owner"
        - "organization:member"
        - "organization:info"


types:
  Label:
    type: string
    maxLength: 50
    minLength: 2
    pattern: ^[a-zA-Z\d\-_\s]{2,50}$

  UserAPIKey:
    description: User specific API key
    type: object
    properties:
      username: string
      apikey: string
      applicationid: string
      scopes: string[]
      label: Label

  PublicKey:
    description: PublicKey of a user
    type: object
    properties:
      label: Label
      publickey: string

  AuthorizationMap:
    description: Mapping between requested labels and real labels
    type: object
    properties:
      requestedlabel: Label
      reallabel: Label

  Authorization:
    description: For an explanation about scopes and scopemapping, see https://git.gig.tech/gig-meneja/iam/blob/master/docs/oauth2/scopes.md
    type: object
    properties:
      username: string
      name: boolean
      grantedTo:
        type: string
        description: The organization that is granted read access to some of the user's information
      publicKeys?: AuthorizationMap[]
      emailaddresses?:
        description: Mapping of the labels of the granted ones to the labels the organization requested.
        type: AuthorizationMap[]
      validatedemailaddresses?:
        description: Mapping of the labels of the granted ones to the labels the organization requested.
        type: AuthorizationMap[]
      phonenumbers?:
        description: Mapping of the labels of the granted ones to the labels the organization requested.
        type: AuthorizationMap[]
      validatedphonenumbers?:
        description: Mapping of the labels of the granted ones to the labels the organization requested.
        type: AuthorizationMap[]
      organizations:
        type: string[]
        description: List of organizations the requesting organization can see your membership of.
      suborganizations:
        type: string[]
        description: List of organizations the requesting organization can see your membership of.
      ownedorganizations:
        type: string[]
        description: List of organizations the requesting organization can create suborgs of.

  Phonenumber:
    type: object
    properties:
      phonenumber:
        type: string
        pattern: \+[0-9]{6,50}$
      label: Label

  EmailAddress:
    type: object
    properties:
      label: Label
      emailaddress:
        type: string

  User:
    type: object
    properties:
      username:
        type: string
        maxLength: 30
        pattern: ^[a-z0-9]{2,30}$
        minLength: 2
      firstname: string
      lastname: string
      publicKeys: string[]
      expire?: datetime
      emailaddresses: EmailAddress[]
      phonenumbers: Phonenumber[]

    example:
      username: bob
      firstname: Bob
      lastname: Johnson
      publicKeys:
      - 1GVS7544tvWM2TM13vNH8sboamJzL6mJ36
      expire: 2018-10-20T16:41:41.090Z
      emailaddresses:
      - label: work
        emailaddress: <EMAIL>
      - label: home
        emailaddress: <EMAIL>
      phonenumbers:
      - label: be
        phonenumber: "+3212341234123"
      - label: eg
        phonenumber: "+2012341234123"

  userview:
    type: object
    properties:
      username:
        type: string
      publicKeys?: PublicKey[]
      emailaddresses: EmailAddress[]
      validatedemailaddresses: EmailAddress[]
      phonenumbers: Phonenumber[]
      validatedphonenumbers: Phonenumber[]
      organizations: string[]
      firstname: string
      lastname: string
      ownerof: Ownerof

  Error:
    type: object
    properties:
      error: string

  Ownerof:
    type: object
    properties:
      emailaddresses: EmailAddress[]

  JoinOrganizationInvitation:
    type: object
    properties:
      organization: string
      user: string
      role:
        type: string
        enum: [owner, member, orgowner, orgmember]
      created?: datetime
      status:
        type: string
        enum: [pending, accepted, rejected]
      method:
        type: string
        enum: [website, email, phone]
      emailaddress: string
      phonenumber: string
      isorganization: boolean

    example:
      organization: mycoolsoccerclub
      user: bob
      role: owner
      created: 2016-02-28T16:41:41.090Z
      status: pending
      method: website
      emailaddress: ""
      phonenumber: ""
      isorganization: false

  Organization:
    type: object
    properties:
      globalid:
        type: string
        minLength: 3
        maxLength: 150
        pattern: ^[a-z\d\-_\s]{3,150}$
      publicKeys:
        type: string[]
        maxItems: 20
      owners:
        type: string[]
        maxItems: 20
        description: List of users `usernames`.
      members:
        type: string[]
        maxItems: 2000
        description: List of users who are member in this organization.
      orgowners:
        type: string[]
        description: List of other organizations who act as an owner in this organization.
      orgmembers:
        type: string[]
        description: List of other organizations who act as a member in this organization.
      includes:
        type: string[]
        maxItems: 100
        description: globalId of sub organizations
      requiredscopes:
        type: RequiredScope[]
        maxItems: 20
        description: List of scopes that are required to join this organization
      includesuborgsof:
        type: string[]
        description: List of orgowners and orgmembers who's children should be included in the organizations membershi or ownership hierarchy

    example:
      globalid: greenitglobe
      publicKeys:
      - 1A9WWh6iAE4RZGN7axy6xZbuWuLknqWLrV
      owners:
      - user1
      - user2
      - user3
      members:
      - user4
      orgowners:
      - organization1
      orgmembers:
      - organization2
      - organization3
      includes:
      - admin.greenitglobe.com
      requiredscopes:
      - scope: user:name
        accessscopes:
        - organization:owner
        - organization:member
      - scope: user:email:personal email
        accessscopes:
        - organization:owner
      includesuborgsof:
        - organization2

  OrganizationUser:
    type: object
    properties:
      username: string
      role:
        type: string
        description: role of the user in this organization, either organization:member or organization:owner
      missingscopes:
        type: string[]
        description: list of required roles that the user didn't share yet.

  GetOrganizationUsersResponseBody:
    type: object
    properties:
      haseditpermissions: boolean
      users: OrganizationUser[]

  OrganizationTreeItem:
    type: object
    properties:
      globalid:
        type: string
      children: OrganizationTreeItem[]

  Member:
    type: object
    properties:
      username:
        type: string
        description: Used when assigning a member to an organization.

    example:
      username: bob

  OrganizationAPIKey:
    type: object
    properties:
      label: Label
      callbackURL?:
        type: string
        maxLength: 250
      clientCredentialsGrantType?:
        description: Indicates if this key may be used in a client credentials oauth2 flow.
        type: boolean
        default: false
      secret?:
        type: string
        maxLength: 250

  KeyStoreKey:
    type: object
    properties:
      label: Label
      username?: string
      globalid?: string
      key: string
      keydata: KeyData

  KeyData:
    type: object
    properties:
      timestamp?: datetime
      comment?: string
      algorithm: string

  RequiredScope:
    type: object
    properties:
      scope:
        type: string
        maxLength: 1024
        description: Scope which should be shared with an organization
      accessscopes:
        type: string[]
        description: Which users can see the shared information.

  LocalizedInfoText:
    type: object
    properties:
      langkey: string
      text: string

  Membership:
    type: object
    properties:
      username: string
      role: string

  MissingScopes:
    type: object
    properties:
      organization: string
      scopes: string[]

  IyoID:
    description: Mapping between an Iyo ID, username and azp
    type: object
    properties:
      username: string
      iyoids: string[]
      azp: string

  IsMember:
    type: object
    properties:
      isMember: boolean
      isOwner: boolean

  ValidityTime:
    type: integer

  APIKeyLabel:
    type: string

  OrganizationLogo:
    type: object
    properties:
      logo: string

  Notification:
    type: object
    properties:
      invitations: JoinOrganizationInvitation[]
      approvals: JoinOrganizationInvitation[]
      missingscopes : MissingScopes[]
      organizationinvitations: JoinOrganizationInvitation[]

  PhoneNumberValidation:
    type: object
    properties:
      validationkey: string
      validated: boolean

  EmailAddressValidation:
    type: object
    properties:
      validationkey: string

  TOTPSecret:
    type: object
    properties:
      totpcode:
        type: string
        description: This is a code that is generated by an authenticator application (e.g. Google Authenticator)
      totpsecret:
        type: string
        description: The totp secret

  TOTPSecretIssuer:
    type: object
    properties:
      totpissuer:
        type: string
        description: This is the issuer of the TOTP
      totpsecret:
        type: string
        description: The totp secret

  TwoFAMethods:
    type: object
    properties:
      totp: boolean
      disable_email_2fa: boolean
      sms: Phonenumber[]

  UserOrganizations:
    type: object
    properties:
      owner: string[]
      member: string[]

  Grant:
    type: string
    minLength: 2
    maxLength: 100
    pattern: ^[a-zA-Z0-9\-_]{2,100}$

  UserGrants:
    type: Grant[]
    description: All the grants a user has

  CreateGrantBody:
    type: object
    properties:
      username:
        type: string
        description: User identifier of the user for whom to add the grant
      grant:
        type: Grant
        description: The grant to add for the user

  UpdateGrantBody:
    type: object
    properties:
      username:
        type: string
        description: User identifier of the user for whom to add the grant
      oldgrant:
        type: Grant
        description: The old grant, to be replaced
      newgrant:
        type: Grant
        description: The new grant which will replace the old grant

  AddOrganizationMember:
    type: object
    properties:
      searchstring: string

  UpdateUserName:
    type: object
    properties:
      firstname: string
      lastname: string

  UpdatePassword:
    type: object
    properties:
      currentpassword: string
      newpassword: string

  VerifyEmailAddress:
    type: object
    properties:
      emailcode: string
      validationkey: string

  AddApiKey:
    type: object
    properties:
      label: Label

  UpdateAPIkey:
    type: object
    properties:
      label: Label

  VerifyPhoneNumber:
    type: object
    properties:
      smscode: string
      validationkey: string

  SetOrgMember:
    type: object
    properties:
      orgmember: string

  SetOrgOwner:
    type: object
    properties:
      orgowner: string

  UpdateOrganizationOrgMemberShip:
    type: object
    properties:
      org: string
      role: string

  AddIncludeSubOrgsOf:
    type: object
    properties:
      globalid: string

  UpdateEmailTwoFA:
    type: object
    properties:
      disable_email_2fa: boolean

  OIDCProviderConfig:
    type: object
    properties:
      id:
        type: string
        description: Unique identifier for the OIDC provider
      name:
        type: string
        description: Display name for the OIDC provider
      issuer:
        type: string
        description: The issuer URL for the OIDC provider
      clientId:
        type: string
        description: Client ID for authenticating with the OIDC provider
      clientSecret:
        type: string
        description: Client secret for authenticating with the OIDC provider
      scope:
        type: string
        description: Scope to request from the OIDC provider
      claimKey:
        type: string
        required: false
        description: Optional claim name to validate for access control
      claimValue:
        type: string
        required: false
        description: Required value for the claim key
      active:
        type: boolean
        description: Whether the OIDC provider is active
      createdAt:
        type: integer
        description: Timestamp when the provider was created
      updatedAt:
        type: integer
        description: Timestamp when the provider was last updated

securedBy: [ oauth20 ]
/users:
  post:
    displayName: CreateUser
    description: Create a new user
    body:
      application/json:
        type: User

  /{username}:
    get:
      securedBy: [oauth20: { scopes: [ "user:admin" ] } ]
      displayName: GetUser
      responses:
        200:
          body:
            application/json:
              type: User
    /name:
      securedBy: [oauth20: { scopes: [ "user:admin" ] } ]
      put:
        displayName: UpdateUserName
        description: Update the user his firstname and lastname
        body:
          application/json:
            type: UpdateUserName
        responses:
          204:
            description: Successfully updated firstname and lastname
          404:
            description: User not found

    /password:
      securedBy: [oauth20: { scopes: [ "user:admin" ] } ]
      put:
        displayName: UpdatePassword
        description: Update the user his password
        body:
          application/json:
            type: UpdatePassword
        responses:
          204:
            description: Password successfully updated
          422:
            description: Invalid password format or invallid currentpassword
            body:
              application/json:
                type: Error
    /emailaddresses:
      securedBy: [oauth20: { scopes: [ "user:admin" ] } ]
      get:
        displayName: GetEmailAddresses
        description: Get a list of the user his email addresses.
        queryParameters:
          validated?:
            type: boolean
            description: optional parameter to filter on only validated email addressses
        responses:
          200:
            body:
              application/json:
                type: EmailAddress[]
      post:
        displayName: RegisterNewEmailAddress
        description: Register a new email address
        queryParameters:
          lang:
            type: string
            description: language that the validation email should be in.
        body:
          application/json:
            type: EmailAddress
        responses:
          200:
            description: Validation number sent
            body:
              application/json:
                type: EmailAddressValidation
          201:
              description: Email address registered
          409:
              description: Label is already used.
      /{label}:
        put:
          displayName: UpdateEmailAddress
          description: Updates the label and/or value of an email address
          queryParameters:
            lang:
              type: string
              description: language that the validation email should be in.
          body:
            application/json:
              type: EmailAddress
          responses:
            201:
              description: Updated
            409:
              description: The new label is already used
            412:
              description: Email address is validated and can not be changed, only deleted
        delete:
          displayName: DeleteEmailAddress
          description: Removes an email address
          responses:
            204:
              description: Email address removed.
            409:
              description: The last email address can not be removed.

        /validate:
          post:
            displayName: ValidateEmailAddress
            description: Sends validation email to email address
            queryParameters:
              lang:
                type: string
                description: language that the validation email should be in.
            responses:
              200:
                description: Validation number sent
                body:
                  application/json:
                    type: EmailAddressValidation
          put:
            displayName: VerifyEmailAddress
            description: Verifies an email address
            body:
              application/json:
                type: VerifyEmailAddress
            responses:
              204:
                description: Email address verified
              422:
                description: Invalid validationkey or code
                body:
                  application/json:
                    type: Error
    /apikeys:
      securedBy: [oauth20: { scopes: [ "user:admin" ] } ]
      post:
        displayName: AddApiKey
        description: Adds an APIKey to the user
        body:
          application/json:
            type: AddApiKey
        responses:
          201:
            description: Added a APIKey to the user
            body:
              application/json:
                type: UserAPIKey
          409:
            description: Label is already used.
      get:
        displayName: ListAPIKeys
        description: Lists the API keys
        responses:
          200:
            description: List of API keys
            body:
              application/json:
                type: UserAPIKey[]
      /{label}:
        put:
          displayName: UpdateAPIkey
          description: Updates the label for the API key
          body:
            application/json:
              type: UpdateAPIkey
          responses:
            204:
              description: API key updated
            409:
              description: The new label is already used
        get:
          displayName: GetAPIkey
          description: Get an API key by label
          responses:
            200:
              description: API key
              body:
                application/json:
                  type: UserAPIKey

        delete:
          displayName: DeleteAPIkey
          description: Removes an API key
          responses:
            204:
              description: API key removed.

    /twofamethods:
      securedBy: [oauth20: { scopes: [ "user:admin" ] } ]
      get:
        displayName: GetTwoFAMethods
        description: Get the possible two-factor authentication methods"
        responses:
          200:
            body:
              application/json:
                type: TwoFAMethods
    /emailtwofa:
      securedBy: [oauth20: { scopes: [ "user:admin" ] } ]
      put:
        displayName: UpdateEmailTwoFA
        description: Enable or Disable Email two factor authentication"
        body:
          application/json:
            type: UpdateEmailTwoFA
        responses:
          204:
            description: Successfully updated EMail 2fa
    /totp:
      securedBy: [oauth20: { scopes: [ "user:admin" ] } ]
      get:
        displayName: GetTOTPSecret
        description: 'Get a TOTP secret and issuer that can be used for setting up two-factor authentication.'
        responses:
          200:
            body:
              application/json:
                type: TOTPSecretIssuer
      post:
        displayName: SetupTOTP
        description: Enable two-factor authentication using TOTP.
        body:
          application/json:
            type: TOTPSecret
        responses:
          422:
            description: Invalid totpcode
          204:
            description: TOTP setup successfully
      delete:
        displayName: RemoveTOTP
        description: Disable TOTP two-factor authentication.
        responses:
          409:
            description: Cannot remove TOTP authentication because this is the last available login method
          204:
            description: TOTP successfully removed

  /{username}/info:
    get:
      securedBy: [oauth20: { scopes: [ "user:info", "user:admin" ] } ]
      displayName: GetUserInformation
      description: Get all of the user his information. This will be limited to the scopes that the user has authorized. See https://gig.gitbooks.io/iam/content/oauth2/scopes.html and https://gig.gitbooks.io/iam/content/oauth2/availableScopes.html for more information.
      responses:
        200:
          body:
            application/json:
              type: userview

  /{username}/phonenumbers:
    securedBy: [oauth20: { scopes: [ "user:admin" ] } ]
    get:
      displayName: GetUserPhoneNumbers
      description: List of all of the user his phone numbers.
      queryParameters:
        validated:
          type: string
          description: optional queryParameter to filter on only validated phonenumbers
      responses:
        200:
          body:
            application/json:
              type: Phonenumber[]
    post:
      displayName: RegisterNewUserPhonenumber
      description: Register a new phonenumber
      body:
        application/json:
          type: Phonenumber
      responses:
         200:
           description: Validation number send
           body:
             application/json:
               type: PhoneNumberValidation
         201:
           description: Registered a new phone number
           body:
             application/json:
               type: PhoneNumberValidation
         409:
           description: Label is already used.
    /{label}:
      get:
        displayName: GetUserPhonenumberByLabel
        description: Get the details of a phone number.
        responses:
          200:
            body:
              application/json:
                type: Phonenumber
      put:
        displayName: UpdateUserPhonenumber
        description: Update the label and/or value of an existing phonenumber.
        body:
          application/json:
              type: Phonenumber
        responses:
          201:
              description: Updated
          409:
              description: The new label is already used
          412:
              description: The phonenumber is validated and can't be changed
      delete:
        displayName: DeleteUserPhonenumber
        description: Removes a phonenumber
        responses:
          204:
            description: Phone number removed.
          404:
            description: Phone number not found
          409:
            description: Phone number not removed because it is the last verified one
      /validate:
        post:
          displayName: ValidatePhonenumber
          description: Sends a validation text message to the phone number.
          responses:
            200:
              description: Validation number send
              body:
                application/json:
                  type: PhoneNumberValidation
        put:
          displayName: VerifyPhoneNumber
          description: Verifies a phone number
          body:
            application/json:
              type: VerifyPhoneNumber
          responses:
            204:
              description: Phone number verified
            422:
              description: Invalid validationkey or code
              body:
                application/json:
                  type: Error

  /{username}/notifications:
    securedBy: [oauth20: { scopes: [ "user:admin" ] } ]
    get:
      displayName: GetNotifications
      description: Get the list of notifications, these are pending invitations or approvals or other requests.
      responses:
        200:
          body:
            application/json:
              type: Notification

  /{username}/authorizations:
    securedBy: [oauth20: { scopes: [ "user:admin" ] } ]
    get:
      displayName: GetAllAuthorizations
      description: Get the list of authorizations.
      responses:
        200:
          body:
            application/json:
              type: Authorization[]
    /{grantedTo}:
      get:
        displayName: GetAuthorization
        description: Get the authorization for a specific organization.
        responses:
          200:
            body:
              application/json:
                type: Authorization
          404:
            description: No authorization for this organization was not found.
      delete:
        displayName: DeleteAuthorization
        description: Remove the authorization for an organization, the granted organization will no longer have access the user's information.
        responses:
          204:
            description: Successfully revoked authorization.
      put:
        displayName: UpdateAuthorization
        description: Modify which information an organization is able to see.
        body:
          application/json:
            type: Authorization
        responses:
          201:
            description: Authorization updated successfully.

  /{username}/organizations:
    securedBy: [oauth20: { scopes: [ "user:admin" ] } ]
    get:
      displayName: GetUserOrganizations
      description: Get the list organizations a user is owner or member of
      responses:
        200:
          body:
            application/json:
              type: UserOrganizations
    /{globalid}/leave:
      delete:
        displayName: LeaveOrganization
        description: Removes the user from an organization
        responses:
          404:
            description: Organization not found
            body:
              application/json:
                type: Error
          204:
            description: Successfully removed user from organization
    /{globalid}/roles/{role}:
        post:
          displayName: AcceptMembership
          description: Accept membership in organization
          body:
            application/json:
              type: JoinOrganizationInvitation
          responses:
            201:
              body:
                application/json:
                  type: JoinOrganizationInvitation
        delete:
          displayName: RejectMembership
          description: Reject membership invitation in an organization.
          responses:
            204:
              description: Succesfully rejected invitation.

  /{username}/publickeys:
    securedBy: [oauth20: { scopes: [ "user:admin" ] } ]
    get:
      displayName: ListPublicKeys
      description: Lists all public keys
      responses:
        200:
          body:
            application/json:
              type: PublicKey[]
        404:
          description: Not found

    post:
      displayName: AddPublicKey
      description: Add a public key
      body:
        application/json:
          type: PublicKey
      responses:
        201:
          body:
            application/json:
              type: PublicKey
        404:
          description: Not found
        409:
          description: Duplicate label

    /{label}:
      securedBy: [oauth20: { scopes: [ "user:admin" ] } ]
      get:
        displayName: GetPublicKey
        description: Get a public key
        responses:
          200:
            body:
              application/json:
                type: PublicKey
          404:
            description: Not found

      put:
        displayName: UpdatePublicKey
        description: Upates the label and/or key of an existing public key
        body:
          application/json:
            type: PublicKey
        responses:
          201:
            body:
              application/json:
                type: PublicKey
          404:
            description: Not found
          409:
            description: Duplicate label

      delete:
        displayName: DeletePublicKey
        description: Delete a public key
        responses:
          204:
            description: Public key deleted
          404:
            description: Not found

  /{username}/keystore:
    securedBy: [oauth20: { scopes: [ "user:keystore" ] } ]
    get:
      displayName: GetKeyStore
      description: Lists all keys written to this users keystore by the accessing organization
      responses:
        200:
          body:
            application/json:
              type: KeyStoreKey[]

    post:
      displayName: SaveKeyStoreKey
      description: Saves a new key to this users keystore. The username, globalid and timestamp will be overwritten
      body:
        application/json:
          type: KeyStoreKey
      responses:
        201:
          body:
            application/json:
              type: KeyStoreKey
        409:
          description: A key with this label for this organization in this user's keystore already exists

    /{label}:
      get:
        displayName: GetKeyStoreKey
        description: Gets the key written to this users keystore for the given label by the accessing organization
        responses:
          200:
            body:
              application/json:
                type: KeyStoreKey
          404:
            description: No key found for this label

  /{username}/identifiers:
    get:
      displayName: ListIyoIds
      description: List all generated iyo ids generated for a user by a party
      responses:
        200:
          body:
            application/json:
              type: IyoID

    post:
      displayName: GenerateIyoID
      description: Generate a new iyo id for this user
      responses:
        201:
          body:
            application/json:
              type: IyoID
        409:
          description: Max amount of iyo ids reached for this user - azp relation

  /identifiers/{identifier}:
    get:
      displayName: LookupIyoID
      description: Lookup the username for an iyo id
      responses:
        200:
          body:
            application/json:
              type: IyoID
        404:
          description: The iyo id is not found, or this azp does not have access to look it up


/organizations:
  post:
    displayName: CreateNewOrganization
    description: Create a new organization. 1 user should be in the owners list. Validation is performed to check if the securityScheme allows management on this user.
    body:
      application/json:
        type: Organization
    responses:
      201:
        body:
          application/json:
            type: Organization
      401:
        description: Unauthorized
  /{globalid}:
    get:
      displayName: GetOrganization
      securedBy: [oauth20: { scopes: [ "organization:member", "organization:owner" ] } ]
      description: Get organization info
      responses:
        200:
          body:
            application/json:
              type: Organization
    post:
      securedBy: [oauth20: { scopes: [ "organization:owner" ] } ]
      displayName: CreateNewSubOrganization
      description: Create a new suborganization.
      body:
        application/json:
          type: Organization
      responses:
        201:
          body:
            application/json:
              type: Organization
        404:
          description: Parent organization does not exist
          body:
            application/json:
              type: Error
        422:
          description: Maximum amount of organizations reached
          body:
            application/json:
              type: Error
    delete:
      securedBy: [oauth20: { scopes: [ "organization:owner" ] } ]
      displayName: DeleteOrganization
      description: Deletes an organization and all data linked to it (join-organization-invitations, oauth_access_tokens, oauth_clients, logo)
      responses:
        204:
          description: Organization and all child organizations deleted

    /grants:
      post:
        securedBy: [oauth20: { scopes: [ "organization:owner" ] } ]
        displayName: CreateUserGrant
        description: Create a new grant for a user
        body:
          application/json:
            type: CreateGrantBody
        responses:
          201:
            body:
              application/json:
                type: UserGrants
          404:
            description: User not found
          409:
            description: Max amount of grants reached for this user

      put:
        securedBy: [oauth20: { scopes: [ "organization:owner" ] } ]
        displayName: UpdateUserGrant
        description: Update an existing grant for a user
        body:
          application/json:
            type: UpdateGrantBody
        responses:
          200:
            body:
              application/json:
                type: UserGrants
          404:
            description: User not found or the specified grant to update does not exist

      /{username}:
        get:
          securedBy: [oauth20: { scopes: [ "organization:owner" ] } ]
          displayName: GetUserGrants
          description: Get all grants for a user
          responses:
            200:
              body:
                application/json:
                  type: UserGrants

        delete:
          securedBy: [oauth20: { scopes: [ "organization:owner" ] } ]
          displayName: DeleteAllUserGrants
          description: Delete all grants for this user
          responses:
            204:
              description: Grants successfully removed

        /{grant}:
          delete:
            securedBy: [oauth20: { scopes: [ "organization:owner" ] } ]
            displayName: DeleteUserGrant
            description: Delete a specified grant for this user
            responses:
              204:
                description: Grant successfully removed

      /havegrant/{grant}:
        get:
          securedBy: [oauth20: { scopes: [ "organization:owner" ] } ]
          displayName: ListUsersWithGrant
          description: Delete a specified grant for this user
          responses:
            200:
              body:
                application/json:
                  type: string[]
                  description: a list off user identifiers of users who have the specified grant

    /description:
      post:
        securedBy: [oauth20: { scopes: [ "organization:owner" ] } ]
        displayName: SetDescription
        description: Set the description for this organization for a given language key
        body:
          application/json:
            type: LocalizedInfoText
        responses:
           201:
             description: description set successfully
             body:
               application/json:
                 type: LocalizedInfoText

      put:
        securedBy: [oauth20: { scopes: [ "organization:owner" ] } ]
        displayName: UpdateDescription
        description: Update the description for this organization for a given language key
        body:
          application/json:
            type: LocalizedInfoText
        responses:
          200:
            description: description updated successfully
            body:
              application/json:
                type: LocalizedInfoText

      /{langkey}:
        get:
          displayName: GetDescription
          description: Get the description for an organization for this langkey
          responses:
            200:
              description: Description retrieved successfully
              body:
                application/json:
                  type: LocalizedInfoText
        delete:
          securedBy: [oauth20: { scopes: [ "organization:owner" ] } ]
          displayName: DeleteDescription
          description: Delete the description for this organization for a given language key
          responses:
            204:
              description: Description deleted successfully
        /withfallback:
          get:
            displayName: GetDescriptionWithFallback
            description: Get the description for an organization for this langkey, try to use the English is there is no description for this langkey
            responses:
              200:
                description: Description retrieved successfully
                body:
                  application/json:
                    type: LocalizedInfoText

    /logo:
      get:
        displayName: GetOrganizationLogo
        description: Get the Logo from an organization
        responses:
          200:
            description: logo retrieved successfully
            body:
              application/json:
                type: OrganizationLogo
      put:
        displayName: SetOrganizationLogo
        securedBy: [oauth20: { scopes: [ "organization:owner" ] } ]
        description: Set the organization Logo for the organization
        body:
          application/json:
            type: OrganizationLogo
        responses:
          200:
            description: updated successfully
            body:
              application/json:
                type: OrganizationLogo
          413:
            description: file size too large

      delete:
        displayName: DeleteOrganizationLogo
        securedBy: [oauth20: { scopes: [ "organization:owner" ] } ]
        description: Removes the Logo from an organization
        responses:
          204:
            description: Logo deleted

    /2fa:
      /validity:
        get:
          displayName: Get2faValidityTime
          description: Get the 2FA validity time for the organization, in seconds
          responses:
            200:
              description: Get the 2FA validity time for this organization
              body:
                application/json:
                  type: ValidityTime
            404:
              description: Organization not found
        put:
          displayName: Set2faValidityTime
          securedBy: [oauth20: { scopes: [ "organization:owner" ] } ]
          description: Update the 2FA validity time for the organization
          body:
            application/json:
              type: ValidityTime
          responses:
            200:
              description: Updated successfully

    /orgmembers:
      securedBy: [oauth20: { scopes: [ "organization:owner" ] } ]
      post:
        displayName: SetOrgMember
        description: Add another organization as a member of this one
        body:
          application/json:
            type: SetOrgMember
        responses:
          201:
            description: Organization successfully added as a member
          404:
            description: Organization not found
          409:
            description: Organization is already an owner or a member
          422:
            description: Max amount of invitations reached.

      put:
        displayName: UpdateOrganizationOrgMemberShip
        description: Update the membership status of an organization
        body:
          application/json:
            type: UpdateOrganizationOrgMemberShip
        responses:
          200:
            description: updated organization membership successfully
            body:
              application/json:
                type: Organization
          404:
            description: Organization not found

      /includesuborgs:
        securedBy: [oauth20: { scopes: [ "organization:owner" ] } ]
        post:
          displayName: AddIncludeSubOrgsOf
          description: Add an orgmember or orgowner organization to the includesuborgsof list
          body:
            application/json:
              type: AddIncludeSubOrgsOf
          responses:
            201:
              description: organization addedd successfully
              body:
                application/json:
                  type: Organization
            404:
              description: Organization not found
            409:
              description: Organization is already in the list

        /{orgmember}:
          securedBy: [oauth20: { scopes: [ "organization:owner" ] } ]
          delete:
            displayName: RemoveIncludeSubOrgsOf
            description: Remove an orgmember or orgowner organization to the includesuborgsof list
            responses:
              204:
                description: organization removed from includelist
              404:
                description: organization not found

      /{globalid2}:
        delete:
          displayName: DeleteOrgMember
          description: Remove an organization as a member
          responses:
            204:
              description: Organization member removed successfully
            404:
              description: The user or the organization does not exist.
    /users:
      get:
        displayName: GetOrganizationUsers
        securedBy: [oauth20: { scopes: [ "organization:owner", "organization:member" ] } ]
        description: Get all users from this organization, not including suborganizations.
        responses:
          200:
            body:
              application/json:
                type: GetOrganizationUsersResponseBody
      /ismember/{username}:
        get:
          displayName: UserIsMember
          securedBy: [oauth20: { scopes: [ "organization:owner" ] } ]
          description: Checks if the user has memberschip rights on the organization
          responses:
            200:
              body:
                application/json:
                  type: IsMember
    /orgowners:
      securedBy: [oauth20: { scopes: [ "organization:owner" ] } ]
      post:
        displayName: SetOrgOwner
        description: Add another organization as an owner of this one
        body:
          application/json:
            type: SetOrgOwner
        responses:
          201:
            description: Organization successfully added as an owner
          404:
            description: Organization not found
          409:
            description: Organization is already an owner or a member
          422:
            description: Max amount of invitations reached.

      /{globalid2}:
        delete:
          displayName: DeleteOrgOwner
          description: Remove an organization as an owner
          responses:
            204:
              description: Organization owner removed successfully
            404:
              description: The organization does not exist.

    /organizations/{invitingorg}/roles/{role}:
      securedBy: [oauth20: { scopes: [ "organization:owner" ] } ]
      post:
        displayName: AcceptOrganizationInvite
        description: Accept the invite for one of your organizations
        body:
          application/json:
            type: JoinOrganizationInvitation
        responses:
          201:
            description: Invite accepted
            body:
              application/json:
                type: JoinOrganizationInvitation
          404:
            description: Invite not found

      delete:
        displayName: RejectOrganizationInvite
        description: Reject the invite for one of your organizations
        responses:
          204:
            description: Invite rejected
          404:
            description: Invite not found


    /members:
      securedBy: [oauth20: { scopes: [ "organization:owner" ] } ]
      put:
        displayName: UpdateOrganizationMemberShip
        description: Update an organization membership
        body:
          application/json:
            type: Membership
        responses:
          200:
            description: updated successfully
            body:
              application/json:
                description: The updated organization
                type: Organization
          404:
            description: Member not found
          422:
            description: Maximum amount of invites reached
            body:
              application/json:
                type: Error

      post:
        displayName: AddOrganizationMember
        description: Invite someone to become member of an organization.
        body:
          application/json:
            type: AddOrganizationMember
        queryParameters:
          invitenotification?:
            type: string
            description: Set to `none` to suppress sending an sms or email
        responses:
          201:
            description: Member assigned successfully
            body:
              application/json:
                type: JoinOrganizationInvitation
          404:
            description: Not found

      /{username}:
        delete:
          displayName: RemoveOrganizationMember
          description: Remove a member from an organization.
          responses:
            204:
              description: Member deleted successfully
            404:
              description: The user or the organization does not exist.

    /owners:
      securedBy: [oauth20: { scopes: [ "organization:owner" ] } ]
      post:
        displayName: AddOrganizationOwner
        description: Invite someone to become owner of an organization.
        body:
          application/json:
            type: Member
        queryParameters:
          invitenotification?:
            type: string
            description: Set to `none` to suppress sending an sms or email
        responses:
          201:
            description: Invite created successfully
            body:
              application/json:
                type: JoinOrganizationInvitation
          404:
            description: The user or the organization does not exist.
          409:
            description: The user already is an owner.

      /{username}:
        delete:
          displayName: RemoveOrganizationOwner
          description: Remove an owner from organization
          responses:
            204:
              description: Owner removed successfully
            401:
              description: Unauthorized
            404:
              description: The user or the organization does not exist.

    /invitations:
      securedBy: [oauth20: { scopes: [ "organization:owner" ] } ]
      get:
        displayName: GetInvitations
        description: Get the list of pending invitations for users to join this organization.
        queryParameters:
          status:
            type: string
            description: What status to filter the invitations on. Possible values are pending, accepted and rejected. When not provided, defaults to pending.
            default: pending
            required: false
            enum:
              - pending
              - accepted
              - rejected
        responses:
          200:
            body:
              application/json:
                type: JoinOrganizationInvitation[]

      /user:
        /{username}:
          delete:
            displayName: RemovePendingOrganizationInvitation
            description: Cancel a pending user invitation.
            responses:
              204:
                description: Invitation cancelled

      /organization:
        /{globalid2}:
          delete:
            displayName: RemovePendingOrg2OrgInvitation
            description: Cancel a pending organization invitation.
            responses:
              204:
                description: Invitation cancelled

    /apikeys:
      securedBy: [oauth20: { scopes: [ "organization:owner" ] } ]
      get:
        displayName: GetOrganizationAPIKeyLabels
        description: Get the list of active api keys.
        responses:
          200:
            body:
              application/json:
                type: APIKeyLabel[]
      post:
        displayName: CreateNewOrganizationAPIKey
        description: Create a new API Key, a secret itself should not be provided, it will be generated serverside.
        body:
          application/json:
            type: OrganizationAPIKey
        responses:
          201:
            body:
              application/json:
                type: OrganizationAPIKey
          409:
            description: Label is already used.
      /{label}:
        get:
          displayName: GetOrganizationAPIKey
          description: Get an api key from an organization
          responses:
            200:
              body:
                application/json:
                 type: OrganizationAPIKey
            404:
              description: No API key with this label found
        put:
          displayName: UpdateOrganizationAPIKey
          description: Updates the label or other properties of a key.
          body:
            application/json:
              type: OrganizationAPIKey
          responses:
            200:
                description: Updated
            404:
                description: Apikey not found
            409:
                description: New label is already used
        delete:
          displayName: DeleteOrganizationAPIKey
          description: Removes an API key
          responses:
            204:
              description: API key removed

    /tree:
      securedBy: [oauth20: { scopes: [ "organization:owner", "organization:member" ] } ]
      get:
        displayName: GetOrganizationTree
        description: Tree structure of all suborganizations
        responses:
          200:
            body:
              application/json:
                type: OrganizationTreeItem

    /requiredscopes:
      securedBy: [oauth20: { scopes: [ "organization:owner" ] } ]
      post:
        displayName: AddRequiredScope
        description: Adds a required scope
        body:
          application/json:
            type: RequiredScope
        responses:
          201:
            description: Added the required scope to the organization.
          409:
            description: The required scope conflicts with an existing one
      /{requiredscope}:
        put:
          displayName: UpdateRequiredScope
          description: Updates a required scope
          responses:
            204:
              description: Updated the required scope
            404:
              description: The required scope was not found.
            409:
              description: The new required scope conflicts with an existing one
        delete:
          displayName: DeleteRequiredScope
          description: Deletes a required scope
          responses:
            204:
              description: Deleted the required scope
            404:
              description: The required scope was not found.

/oidc_providers:
  securedBy: [oauth20: { scopes: [ "user:admin" ] } ]
  get:
    displayName: ListOIDCProviders
    description: List all OIDC provider configurations
    queryParameters:
      active:
        type: boolean
        description: Filter to only return active providers
    responses:
      200:
        body:
          application/json:
            type: OIDCProviderConfig[]

  /{id}:
    securedBy: [oauth20: { scopes: [ "user:admin" ] } ]
    get:
      displayName: GetOIDCProvider
      description: Get an OIDC provider configuration by ID
      responses:
        200:
          body:
            application/json:
              type: OIDCProviderConfig
        404:
          description: Provider not found
